// Proceso principal de Electron
const { app, BrowserWindow, ipcMain } = require('electron');
const path = require('path');
const { spawn } = require('child_process');
const os = require('os');

let mainWindow;

// Función para obtener el UUID del sistema usando PowerShell
const getSystemUUID = () => {
  return new Promise((resolve, reject) => {
    const scriptPath = path.join(__dirname, '..', 'scripts', 'getSystemUUID.ps1');
    
    // Ejecutar PowerShell de forma oculta
    const powershell = spawn('powershell.exe', [
      '-WindowStyle', 'Hidden',
      '-ExecutionPolicy', 'Bypass',
      '-File', scriptPath
    ], {
      windowsHide: true,
      stdio: ['pipe', 'pipe', 'pipe']
    });

    let output = '';
    let errorOutput = '';

    powershell.stdout.on('data', (data) => {
      output += data.toString();
    });

    powershell.stderr.on('data', (data) => {
      errorOutput += data.toString();
    });

    powershell.on('close', (code) => {
      if (code === 0 && output.trim()) {
        resolve(output.trim());
      } else {
        // Fallback: usar información del sistema
        const fallbackId = `${os.hostname()}-${os.userInfo().username}-${os.platform()}`;
        resolve(fallbackId);
      }
    });

    powershell.on('error', (error) => {
      // Fallback en caso de error
      const fallbackId = `${os.hostname()}-${os.userInfo().username}-${os.platform()}`;
      resolve(fallbackId);
    });
  });
};

// Crear la ventana principal
const createWindow = () => {
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js')
    },
    icon: path.join(__dirname, '..', 'assets', 'icon.png'), // Opcional
    title: 'Battle Strategy Creator'
  });

  // Cargar la aplicación
  if (process.env.NODE_ENV === 'development') {
    mainWindow.loadURL('http://localhost:5173');
    mainWindow.webContents.openDevTools();
  } else {
    mainWindow.loadFile(path.join(__dirname, '..', 'dist', 'index.html'));
  }
};

// Manejadores IPC
ipcMain.handle('get-system-uuid', async () => {
  try {
    return await getSystemUUID();
  } catch (error) {
    console.error('Error obteniendo UUID:', error);
    throw error;
  }
});

ipcMain.handle('is-development', () => {
  return process.env.NODE_ENV === 'development';
});

ipcMain.handle('get-system-info', () => {
  return {
    platform: os.platform(),
    arch: os.arch(),
    hostname: os.hostname(),
    username: os.userInfo().username
  };
});

// Eventos de la aplicación
app.whenReady().then(createWindow);

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});
