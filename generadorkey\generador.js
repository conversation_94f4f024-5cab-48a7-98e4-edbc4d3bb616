// Generador de Claves de Activación
// Sistema independiente para generar claves de 16 dígitos
import CryptoJS from 'crypto-js';

/**
 * Genera una clave de activación usando HMAC SHA256
 * @param {string} uuid - UUID del sistema o MAC Address
 * @param {string} secretKey - Clave secreta
 * @returns {string} Clave de activación formateada
 */
export const generarClaveActivacion = (uuid, secretKey) => {
  // Validar parámetros
  if (!uuid || typeof uuid !== 'string' || uuid.trim() === '') {
    throw new Error('UUID/MAC Address es requerido y debe ser una cadena válida');
  }
  
  if (!secretKey || typeof secretKey !== 'string' || secretKey.trim() === '') {
    throw new Error('Clave secreta es requerida y debe ser una cadena válida');
  }
  
  try {
    // Normalizar UUID/MAC (remover guiones, espacios, dos puntos y convertir a mayúsculas)
    const uuidNormalizado = uuid.replace(/[-\s:]/g, '').toUpperCase();

    // Validar formato: UUID (32 chars) o MAC Address (12 chars) - ambos hexadecimales
    if (!/^[0-9A-F]{12}$/.test(uuidNormalizado) && !/^[0-9A-F]{32}$/.test(uuidNormalizado)) {
      throw new Error('Debe ser UUID válido (32 chars hex) o MAC Address válida (12 chars hex)');
    }
    
    // Generar HMAC SHA256
    const hmac = CryptoJS.HmacSHA256(uuidNormalizado, secretKey);
    const hmacHex = hmac.toString(CryptoJS.enc.Hex);
    
    // Tomar los primeros 16 caracteres
    const primeros16 = hmacHex.substring(0, 16).toUpperCase();
    
    // Formatear como XXXX-XXXX-XXXX-XXXX
    const claveFormateada = `${primeros16.substring(0, 4)}-${primeros16.substring(4, 8)}-${primeros16.substring(8, 12)}-${primeros16.substring(12, 16)}`;
    
    return claveFormateada;
    
  } catch (error) {
    throw new Error(`Error generando clave: ${error.message}`);
  }
};

/**
 * Valida una clave de activación
 * @param {string} uuid - UUID del sistema
 * @param {string} secretKey - Clave secreta
 * @param {string} claveActivacion - Clave a validar
 * @returns {boolean} True si la clave es válida
 */
export const validarClaveActivacion = (uuid, secretKey, claveActivacion) => {
  try {
    const claveEsperada = generarClaveActivacion(uuid, secretKey);
    return claveEsperada.toLowerCase() === claveActivacion.toLowerCase();
  } catch (error) {
    return false;
  }
};

/**
 * Genera información detallada del proceso
 * @param {string} uuid - UUID del sistema
 * @param {string} secretKey - Clave secreta
 * @returns {object} Información detallada del proceso
 */
export const generarInformacionDetallada = (uuid, secretKey) => {
  const uuidNormalizado = uuid.replace(/[-\s]/g, '').toUpperCase();
  const hmac = CryptoJS.HmacSHA256(uuidNormalizado, secretKey);
  const hmacHex = hmac.toString(CryptoJS.enc.Hex);
  const primeros16 = hmacHex.substring(0, 16).toUpperCase();
  const claveFormateada = generarClaveActivacion(uuid, secretKey);
  
  return {
    uuidOriginal: uuid,
    uuidNormalizado: uuidNormalizado,
    claveSecreta: secretKey,
    hmacCompleto: hmacHex,
    primeros16Digitos: primeros16,
    claveActivacion: claveFormateada,
    longitudHmac: hmacHex.length,
    longitudClave: claveFormateada.length,
    timestamp: new Date().toISOString()
  };
};

// Función principal para uso desde línea de comandos
const main = () => {
  const args = process.argv.slice(2);
  
  if (args.length < 2) {
    console.log('');
    console.log('🔑 GENERADOR DE CLAVES DE ACTIVACIÓN');
    console.log('=====================================');
    console.log('');
    console.log('Uso: node generador.js <MAC_ADDRESS_O_UUID> <CLAVE_SECRETA>');
    console.log('');
    console.log('Ejemplos:');
    console.log('Con MAC Address: node generador.js "AABBCCDDEEFF" "Lucas2Derepredador2025"');
    console.log('Con UUID:        node generador.js "12345678-1234-1234-1234-123456789012" "Lucas2Derepredador2025"');
    console.log('');
    console.log('Parámetros:');
    console.log('  MAC_ADDRESS   - MAC Address del adaptador de red (12 caracteres hex)');
    console.log('  UUID          - UUID del sistema (32 caracteres hex, con o sin guiones)');
    console.log('  CLAVE_SECRETA - Clave secreta para generar HMAC');
    console.log('');
    console.log('💡 Recomendado: Usar MAC Address para mayor estabilidad');
    console.log('   Ejecuta "ejecutar-admin.bat" en la carpeta principal para obtener la MAC real');
    console.log('');
    process.exit(1);
  }
  
  const uuid = args[0];
  const secretKey = args[1];
  
  try {
    console.log('');
    console.log('🔑 GENERADOR DE CLAVES DE ACTIVACIÓN');
    console.log('=====================================');
    console.log('');
    
    const info = generarInformacionDetallada(uuid, secretKey);
    
    console.log(`📋 Identificador Original: ${info.uuidOriginal}`);
    console.log(`🔧 Identificador Normalizado: ${info.uuidNormalizado}`);
    console.log(`🔐 Clave Secreta:        ${info.claveSecreta}`);
    console.log(`📊 HMAC SHA256:          ${info.hmacCompleto}`);
    console.log(`🎯 Primeros 16 dígitos:  ${info.primeros16Digitos}`);
    console.log('');
    console.log('✅ CLAVE DE ACTIVACIÓN GENERADA:');
    console.log(`🔑 ${info.claveActivacion}`);
    console.log('');
    console.log('📝 Información adicional:');
    console.log(`   - Longitud HMAC: ${info.longitudHmac} caracteres`);
    console.log(`   - Longitud clave: ${info.longitudClave} caracteres`);
    console.log(`   - Generado: ${info.timestamp}`);
    console.log('');
    console.log('⚠️  IMPORTANTE: Guarda esta clave de forma segura');
    console.log('=====================================');
    console.log('');
    
  } catch (error) {
    console.error('');
    console.error('❌ ERROR:', error.message);
    console.error('');
    process.exit(1);
  }
};

// Ejecutar si es llamado directamente
main();
