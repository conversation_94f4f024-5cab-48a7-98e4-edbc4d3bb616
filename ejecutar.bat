@echo off
title Battle Strategy Creator - Ejecutar Aplicacion
color 0A

echo.
echo ========================================
echo   BATTLE STRATEGY CREATOR
echo ========================================
echo.
echo Iniciando aplicacion...
echo.

REM Verificar si Node.js esta instalado
node --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Node.js no esta instalado.
    echo Por favor instala Node.js desde https://nodejs.org/
    echo.
    pause
    exit /b 1
)

REM Verificar si las dependencias estan instaladas
if not exist "node_modules" (
    echo Instalando dependencias...
    echo.
    npm install
    if errorlevel 1 (
        echo ERROR: No se pudieron instalar las dependencias.
        echo.
        pause
        exit /b 1
    )
)

REM Ejecutar la aplicacion en modo desarrollo
echo Ejecutando Battle Strategy Creator...
echo.
echo La aplicacion se abrira en: http://localhost:5173
echo.
echo Para detener la aplicacion, presiona Ctrl+C
echo.

npm run dev

echo.
echo La aplicacion se ha cerrado.
pause
