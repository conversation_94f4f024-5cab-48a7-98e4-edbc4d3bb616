import CryptoJS from 'crypto-js';

// Clave secreta ofuscada - no usar 'e' directamente
const getSecretKey = (): string => {
  // Construir la clave de forma ofuscada
  const parts = ['Lucas2D', 'r', 'pr', 'dador2025'];
  const vowel = String.fromCharCode(101); // 'e'
  return parts[0] + vowel + parts[1] + vowel + parts[2] + vowel + parts[3];
};

// Función para generar HMAC SHA256
export const generateHMAC = (message: string): string => {
  const secretKey = getSecretKey();
  const hash = CryptoJS.HmacSHA256(message, secretKey);
  return hash.toString(CryptoJS.enc.Hex);
};

// Función para verificar HMAC
export const verifyHMAC = (message: string, providedHash: string): boolean => {
  const expectedHash = generateHMAC(message);
  return expectedHash.toLowerCase() === providedHash.toLowerCase();
};

// Función para generar clave de activación basada en MAC Address
export const generateActivationKey = (macAddress: string): string => {
  if (!macAddress || macAddress.trim() === '') {
    throw new Error('MAC Address no puede estar vacía');
  }

  // Normalizar MAC Address (remover guiones, dos puntos y convertir a mayúsculas)
  const normalizedMAC = macAddress.replace(/[-:]/g, '').toUpperCase();

  // Validar que sea una MAC Address válida (12 caracteres hexadecimales)
  if (!/^[0-9A-F]{12}$/.test(normalizedMAC)) {
    throw new Error('MAC Address debe tener formato válido (12 caracteres hexadecimales)');
  }

  // Generar HMAC de la MAC Address
  const hmac = generateHMAC(normalizedMAC);

  // Tomar los primeros 16 caracteres y formatear como clave
  const keyPart = hmac.substring(0, 16).toUpperCase();

  // Formatear como XXXX-XXXX-XXXX-XXXX
  return `${keyPart.substring(0, 4)}-${keyPart.substring(4, 8)}-${keyPart.substring(8, 12)}-${keyPart.substring(12, 16)}`;
};

// Función para validar clave de activación
export const validateActivationKey = (macAddress: string, activationKey: string): boolean => {
  try {
    if (!macAddress || !activationKey) {
      return false;
    }

    // Generar la clave esperada para esta MAC Address
    const expectedKey = generateActivationKey(macAddress);

    // Comparar claves (sin importar mayúsculas/minúsculas)
    return expectedKey.toLowerCase() === activationKey.toLowerCase();
  } catch (error) {
    console.error('Error validando clave de activación:', error);
    return false;
  }
};

// Función para obtener MAC Address real del adaptador de red principal
const getRealMACAddress = async (): Promise<string | null> => {
  try {
    // Método 1: Leer desde archivo generado por PowerShell
    try {
      const response = await fetch('/mac_address.json');
      if (response.ok) {
        const data = await response.json();
        if (data.macAddress && data.macAddress.length >= 12) {
          console.log('✅ MAC Address leída desde archivo de PowerShell');
          return data.macAddress.replace(/[:-]/g, '').toUpperCase();
        }
      }
    } catch (fileError) {
      console.log('Archivo MAC no disponible, intentando archivo de texto...');
    }

    // Método 2: Leer desde archivo de texto simple
    try {
      const response = await fetch('/mac_address.txt');
      if (response.ok) {
        const macText = await response.text();
        const cleanMAC = macText.trim().replace(/[:-]/g, '').toUpperCase();

        // Validar formato MAC (12 caracteres hexadecimales)
        if (cleanMAC.match(/^[0-9A-F]{12}$/)) {
          console.log('✅ MAC Address leída desde archivo de texto');
          return cleanMAC;
        }
      }
    } catch (textError) {
      console.log('Archivo de texto MAC no disponible');
    }

    // Método 3: Usar API de Electron si está disponible
    if ((window as any).electronAPI) {
      try {
        const electronMAC = await (window as any).electronAPI.getMACAddress();
        if (electronMAC && electronMAC.length >= 12) {
          console.log('✅ MAC Address obtenida via Electron');
          return electronMAC.replace(/[:-]/g, '').toUpperCase();
        }
      } catch (electronError) {
        console.warn('Error con API de Electron para MAC:', electronError.message);
      }
    }

  } catch (error) {
    console.log('No se pudo obtener MAC Address real:', error.message);
  }

  return null;
};

// Función para generar MAC Address consistente como fallback
const generateConsistentMACAddress = (): string => {
  try {
    // Usar características del navegador y sistema para generar MAC consistente
    const fingerprint = [
      navigator.userAgent,
      navigator.language,
      navigator.platform,
      screen.width + 'x' + screen.height,
      new Date().getTimezoneOffset().toString(),
      navigator.hardwareConcurrency?.toString() || '4',
      navigator.deviceMemory?.toString() || '8'
    ].join('|');

    // Generar hash y convertir a formato MAC
    const hash = CryptoJS.SHA256(fingerprint).toString();
    const macAddress = hash.substring(0, 12).toUpperCase();

    // Formatear como MAC address válida (asegurar que no sea multicast)
    const firstByte = parseInt(macAddress.substring(0, 2), 16);
    const validFirstByte = (firstByte & 0xFE).toString(16).padStart(2, '0').toUpperCase();

    return validFirstByte + macAddress.substring(2);
  } catch (error) {
    console.error('Error generando MAC consistente:', error);
    // MAC de fallback de emergencia
    return '02' + CryptoJS.SHA256('emergency-fallback').toString().substring(0, 10).toUpperCase();
  }
};

// Función para obtener SOLO MAC Address REAL del hardware (sin fallbacks)
export const getSystemMACAddress = async (): Promise<string> => {
  try {
    // PRIORIDAD ÚNICA: MAC Address real del hardware
    const realMAC = await getRealMACAddress();
    if (realMAC && realMAC.length === 12) {
      console.log('🔑 MAC Address REAL obtenida del hardware');
      localStorage.setItem('system_mac_cache', realMAC);
      localStorage.setItem('system_mac_is_real', 'true');
      localStorage.setItem('system_mac_source', 'Hardware Real');
      return realMAC;
    }

    // Verificar cache SOLO si es MAC real
    const storedMAC = localStorage.getItem('system_mac_cache');
    const isRealMAC = localStorage.getItem('system_mac_is_real') === 'true';

    if (storedMAC && storedMAC.length === 12 && isRealMAC) {
      console.log('🔑 MAC Address REAL desde cache');
      return storedMAC;
    }

    // NO HAY FALLBACK - Si no hay MAC real, lanzar error
    throw new Error('No se pudo obtener MAC Address real del hardware. Ejecuta "ejecutar-admin.bat" como administrador.');

  } catch (error) {
    console.error('Error obteniendo MAC Address REAL:', error);
    throw error; // Propagar el error sin fallback
  }
};

// ELIMINADO: Ya no usamos UUID del sistema
// Ahora SOLO usamos MAC Address real del adaptador de red

// Función para generar un UUID de prueba (solo para desarrollo)
export const generateTestUUID = (): string => {
  return 'XXXXXXXX-XXXX-4XXX-YXXX-XXXXXXXXXXXX'.replace(/[XY]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'X' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  }).toUpperCase();
};
