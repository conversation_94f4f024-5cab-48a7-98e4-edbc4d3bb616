import CryptoJS from 'crypto-js';

// Clave secreta ofuscada - no usar 'e' directamente
const getSecretKey = (): string => {
  // Construir la clave de forma ofuscada
  const parts = ['Lucas2D', 'r', 'pr', 'dador2025'];
  const vowel = String.fromCharCode(101); // 'e'
  return parts[0] + vowel + parts[1] + vowel + parts[2] + vowel + parts[3];
};

// Función para generar HMAC SHA256
export const generateHMAC = (message: string): string => {
  const secretKey = getSecretKey();
  const hash = CryptoJS.HmacSHA256(message, secretKey);
  return hash.toString(CryptoJS.enc.Hex);
};

// Función para verificar HMAC
export const verifyHMAC = (message: string, providedHash: string): boolean => {
  const expectedHash = generateHMAC(message);
  return expectedHash.toLowerCase() === providedHash.toLowerCase();
};

// Función para generar clave de activación basada en UUID
export const generateActivationKey = (uuid: string): string => {
  if (!uuid || uuid.trim() === '') {
    throw new Error('UUID no puede estar vacío');
  }
  
  // Normalizar UUID (remover guiones y convertir a mayúsculas)
  const normalizedUUID = uuid.replace(/-/g, '').toUpperCase();
  
  // Generar HMAC del UUID
  const hmac = generateHMAC(normalizedUUID);
  
  // Tomar los primeros 16 caracteres y formatear como clave
  const keyPart = hmac.substring(0, 16).toUpperCase();
  
  // Formatear como XXXX-XXXX-XXXX-XXXX
  return `${keyPart.substring(0, 4)}-${keyPart.substring(4, 8)}-${keyPart.substring(8, 12)}-${keyPart.substring(12, 16)}`;
};

// Función para validar clave de activación
export const validateActivationKey = (uuid: string, activationKey: string): boolean => {
  try {
    if (!uuid || !activationKey) {
      return false;
    }
    
    // Generar la clave esperada para este UUID
    const expectedKey = generateActivationKey(uuid);
    
    // Comparar claves (sin importar mayúsculas/minúsculas)
    return expectedKey.toLowerCase() === activationKey.toLowerCase();
  } catch (error) {
    console.error('Error validando clave de activación:', error);
    return false;
  }
};

// Función para ejecutar PowerShell y obtener UUID real del hardware
const executeGetUUIDPowerShell = async (): Promise<string> => {
  return new Promise((resolve, reject) => {
    try {
      // Comando PowerShell para obtener UUID del sistema
      const command = 'Get-CimInstance -ClassName Win32_ComputerSystemProduct | Select-Object -ExpandProperty UUID';

      // Intentar ejecutar usando diferentes métodos según el entorno
      if ((window as any).electronAPI) {
        // Método 1: Usar API de Electron
        (window as any).electronAPI.executeCommand(command)
          .then((result: string) => {
            if (result && result.trim() && result.trim() !== '00000000-0000-0000-0000-000000000000') {
              resolve(result.trim().toUpperCase());
            } else {
              reject(new Error('UUID no válido obtenido'));
            }
          })
          .catch(reject);
      } else if ((window as any).chrome && (window as any).chrome.runtime) {
        // Método 2: Extensión de Chrome (si está disponible)
        reject(new Error('Extensión de Chrome no implementada'));
      } else {
        // Método 3: Para aplicaciones web, no se puede ejecutar PowerShell directamente
        reject(new Error('No se puede ejecutar PowerShell desde navegador web'));
      }
    } catch (error) {
      reject(error);
    }
  });
};

// Función para obtener UUID del sistema de forma consistente
export const getSystemUUID = async (): Promise<string> => {
  try {
    // Método 1: Verificar si hay un UUID real almacenado previamente
    const storedUUID = localStorage.getItem('system_uuid_cache');
    const isRealUUID = localStorage.getItem('system_uuid_is_real') === 'true';

    if (storedUUID && storedUUID.length === 36 && isRealUUID) {
      return storedUUID;
    }

    // Método 2: Intentar obtener UUID real usando PowerShell
    try {
      const realUUID = await executeGetUUIDPowerShell();
      if (realUUID && realUUID.length === 36) {
        localStorage.setItem('system_uuid_cache', realUUID);
        localStorage.setItem('system_uuid_is_real', 'true');
        return realUUID;
      }
    } catch (powerShellError) {
      console.warn('No se pudo obtener UUID real via PowerShell:', powerShellError.message);
    }

    // Método 3: Usar la API de Electron si está disponible
    if ((window as any).electronAPI) {
      try {
        const electronUUID = await (window as any).electronAPI.getSystemUUID();
        if (electronUUID && electronUUID.length === 36) {
          localStorage.setItem('system_uuid_cache', electronUUID);
          localStorage.setItem('system_uuid_is_real', 'true');
          return electronUUID;
        }
      } catch (electronError) {
        console.warn('Error con API de Electron:', electronError.message);
      }
    }

    // Método 4: Para aplicaciones web, usar UUID consistente como fallback
    const consistentUUID = generateConsistentUUID();

    // Solo almacenar si no hay un UUID real ya guardado
    if (!storedUUID) {
      localStorage.setItem('system_uuid_cache', consistentUUID);
      localStorage.setItem('system_uuid_is_real', 'false');
    }

    return storedUUID || consistentUUID;

  } catch (error) {
    console.error('Error obteniendo UUID del sistema:', error);

    // Fallback final: usar UUID consistente
    const fallbackUUID = generateConsistentUUID();
    localStorage.setItem('system_uuid_cache', fallbackUUID);
    localStorage.setItem('system_uuid_is_real', 'false');
    return fallbackUUID;
  }
};

// Función para generar un UUID consistente basado en características del sistema
export const generateConsistentUUID = (): string => {
  // Crear un identificador basado en características del navegador/sistema
  const fingerprint = [
    navigator.userAgent,
    navigator.language,
    screen.width,
    screen.height,
    screen.colorDepth,
    new Date().getTimezoneOffset(),
    navigator.platform,
    navigator.hardwareConcurrency || 4
  ].join('|');

  // Generar hash simple del fingerprint
  let hash = 0;
  for (let i = 0; i < fingerprint.length; i++) {
    const char = fingerprint.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convertir a 32bit integer
  }

  // Convertir hash a UUID format
  const hex = Math.abs(hash).toString(16).padStart(8, '0');
  const uuid = `${hex.substring(0, 8)}-${hex.substring(0, 4)}-4${hex.substring(1, 4)}-8${hex.substring(2, 5)}-${hex}${hex.substring(0, 4)}`;

  return uuid.toUpperCase();
};

// Función para generar un UUID de prueba (solo para desarrollo)
export const generateTestUUID = (): string => {
  // En desarrollo, usar UUID consistente basado en el sistema
  return generateConsistentUUID();
};
