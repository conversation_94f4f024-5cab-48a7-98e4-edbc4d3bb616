import CryptoJS from 'crypto-js';

// Clave secreta ofuscada - no usar 'e' directamente
const getSecretKey = (): string => {
  // Construir la clave de forma ofuscada
  const parts = ['Lucas2D', 'r', 'pr', 'dador2025'];
  const vowel = String.fromCharCode(101); // 'e'
  return parts[0] + vowel + parts[1] + vowel + parts[2] + vowel + parts[3];
};

// Función para generar HMAC SHA256
export const generateHMAC = (message: string): string => {
  const secretKey = getSecretKey();
  const hash = CryptoJS.HmacSHA256(message, secretKey);
  return hash.toString(CryptoJS.enc.Hex);
};

// Función para verificar HMAC
export const verifyHMAC = (message: string, providedHash: string): boolean => {
  const expectedHash = generateHMAC(message);
  return expectedHash.toLowerCase() === providedHash.toLowerCase();
};

// Función para generar clave de activación basada en UUID
export const generateActivationKey = (uuid: string): string => {
  if (!uuid || uuid.trim() === '') {
    throw new Error('UUID no puede estar vacío');
  }
  
  // Normalizar UUID (remover guiones y convertir a mayúsculas)
  const normalizedUUID = uuid.replace(/-/g, '').toUpperCase();
  
  // Generar HMAC del UUID
  const hmac = generateHMAC(normalizedUUID);
  
  // Tomar los primeros 16 caracteres y formatear como clave
  const keyPart = hmac.substring(0, 16).toUpperCase();
  
  // Formatear como XXXX-XXXX-XXXX-XXXX
  return `${keyPart.substring(0, 4)}-${keyPart.substring(4, 8)}-${keyPart.substring(8, 12)}-${keyPart.substring(12, 16)}`;
};

// Función para validar clave de activación
export const validateActivationKey = (uuid: string, activationKey: string): boolean => {
  try {
    if (!uuid || !activationKey) {
      return false;
    }
    
    // Generar la clave esperada para este UUID
    const expectedKey = generateActivationKey(uuid);
    
    // Comparar claves (sin importar mayúsculas/minúsculas)
    return expectedKey.toLowerCase() === activationKey.toLowerCase();
  } catch (error) {
    console.error('Error validando clave de activación:', error);
    return false;
  }
};

// Función para obtener MAC Address real del adaptador de red principal
const getRealMACAddress = async (): Promise<string | null> => {
  try {
    // Método 1: Leer desde archivo generado por PowerShell
    try {
      const response = await fetch('/mac_address.json');
      if (response.ok) {
        const data = await response.json();
        if (data.macAddress && data.macAddress.length >= 12) {
          console.log('✅ MAC Address leída desde archivo de PowerShell');
          return data.macAddress.replace(/[:-]/g, '').toUpperCase();
        }
      }
    } catch (fileError) {
      console.log('Archivo MAC no disponible, intentando archivo de texto...');
    }

    // Método 2: Leer desde archivo de texto simple
    try {
      const response = await fetch('/mac_address.txt');
      if (response.ok) {
        const macText = await response.text();
        const cleanMAC = macText.trim().replace(/[:-]/g, '').toUpperCase();

        // Validar formato MAC (12 caracteres hexadecimales)
        if (cleanMAC.match(/^[0-9A-F]{12}$/)) {
          console.log('✅ MAC Address leída desde archivo de texto');
          return cleanMAC;
        }
      }
    } catch (textError) {
      console.log('Archivo de texto MAC no disponible');
    }

    // Método 3: Usar API de Electron si está disponible
    if ((window as any).electronAPI) {
      try {
        const electronMAC = await (window as any).electronAPI.getMACAddress();
        if (electronMAC && electronMAC.length >= 12) {
          console.log('✅ MAC Address obtenida via Electron');
          return electronMAC.replace(/[:-]/g, '').toUpperCase();
        }
      } catch (electronError) {
        console.warn('Error con API de Electron para MAC:', electronError.message);
      }
    }

  } catch (error) {
    console.log('No se pudo obtener MAC Address real:', error.message);
  }

  return null;
};

// Función para generar MAC Address consistente como fallback
const generateConsistentMACAddress = (): string => {
  try {
    // Usar características del navegador y sistema para generar MAC consistente
    const fingerprint = [
      navigator.userAgent,
      navigator.language,
      navigator.platform,
      screen.width + 'x' + screen.height,
      new Date().getTimezoneOffset().toString(),
      navigator.hardwareConcurrency?.toString() || '4',
      navigator.deviceMemory?.toString() || '8'
    ].join('|');

    // Generar hash y convertir a formato MAC
    const hash = CryptoJS.SHA256(fingerprint).toString();
    const macAddress = hash.substring(0, 12).toUpperCase();

    // Formatear como MAC address válida (asegurar que no sea multicast)
    const firstByte = parseInt(macAddress.substring(0, 2), 16);
    const validFirstByte = (firstByte & 0xFE).toString(16).padStart(2, '0').toUpperCase();

    return validFirstByte + macAddress.substring(2);
  } catch (error) {
    console.error('Error generando MAC consistente:', error);
    // MAC de fallback de emergencia
    return '02' + CryptoJS.SHA256('emergency-fallback').toString().substring(0, 10).toUpperCase();
  }
};

// Función para obtener MAC Address del sistema (real o consistente)
export const getSystemMACAddress = async (): Promise<string> => {
  try {
    // Método 1: PRIORIDAD MÁXIMA - MAC Address real del hardware
    const realMAC = await getRealMACAddress();
    if (realMAC && realMAC.length === 12) {
      console.log('🔑 MAC Address real obtenida del hardware');
      localStorage.setItem('system_mac_cache', realMAC);
      localStorage.setItem('system_mac_is_real', 'true');
      localStorage.setItem('system_mac_source', 'Hardware Real');
      return realMAC;
    }

    // Método 2: Verificar cache con MAC real
    const storedMAC = localStorage.getItem('system_mac_cache');
    const isRealMAC = localStorage.getItem('system_mac_is_real') === 'true';

    if (storedMAC && storedMAC.length === 12 && isRealMAC) {
      console.log('🔑 MAC Address real desde cache');
      return storedMAC;
    }

    // Método 3: Generar MAC consistente como fallback
    const consistentMAC = generateConsistentMACAddress();

    // Solo almacenar si no hay una MAC real ya guardada
    if (!storedMAC) {
      console.log('⚠️ MAC Address generada de forma consistente (FALLBACK)');
      localStorage.setItem('system_mac_cache', consistentMAC);
      localStorage.setItem('system_mac_is_real', 'false');
      localStorage.setItem('system_mac_source', 'Generated Consistent');
    }

    return storedMAC || consistentMAC;

  } catch (error) {
    console.error('Error obteniendo MAC Address del sistema:', error);

    // Fallback final: MAC de emergencia
    const emergencyMAC = generateConsistentMACAddress();
    console.log('❌ MAC Address de emergencia generada');
    localStorage.setItem('system_mac_cache', emergencyMAC);
    localStorage.setItem('system_mac_is_real', 'false');
    localStorage.setItem('system_mac_source', 'Emergency Fallback');
    return emergencyMAC;
  }
};

// Función para obtener UUID del sistema (ahora basado en MAC Address)
export const getSystemUUID = async (): Promise<string> => {
  try {
    // Obtener MAC Address del sistema
    const macAddress = await getSystemMACAddress();

    // Convertir MAC Address a formato UUID-like para compatibilidad
    // Formato: XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX
    const uuid = `${macAddress.substring(0, 8)}-${macAddress.substring(8, 12)}-4${macAddress.substring(0, 3)}-A${macAddress.substring(3, 6)}-${macAddress}${macAddress.substring(0, 4)}`;

    return uuid.toUpperCase();
  } catch (error) {
    console.error('Error obteniendo UUID basado en MAC:', error);
    // Fallback de emergencia
    const emergencyMAC = generateConsistentMACAddress();
    const uuid = `${emergencyMAC.substring(0, 8)}-${emergencyMAC.substring(8, 12)}-4${emergencyMAC.substring(0, 3)}-A${emergencyMAC.substring(3, 6)}-${emergencyMAC}${emergencyMAC.substring(0, 4)}`;
    return uuid.toUpperCase();
  }
};

// Función para generar un UUID de prueba (solo para desarrollo)
export const generateTestUUID = (): string => {
  return 'XXXXXXXX-XXXX-4XXX-YXXX-XXXXXXXXXXXX'.replace(/[XY]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'X' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  }).toUpperCase();
};
