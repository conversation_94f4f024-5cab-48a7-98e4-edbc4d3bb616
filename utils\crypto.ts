import CryptoJS from 'crypto-js';

// Clave secreta ofuscada - no usar 'e' directamente
const getSecretKey = (): string => {
  // Construir la clave de forma ofuscada
  const parts = ['Lucas2D', 'r', 'pr', 'dador2025'];
  const vowel = String.fromCharCode(101); // 'e'
  return parts[0] + vowel + parts[1] + vowel + parts[2] + vowel + parts[3];
};

// Función para generar HMAC SHA256
export const generateHMAC = (message: string): string => {
  const secretKey = getSecretKey();
  const hash = CryptoJS.HmacSHA256(message, secretKey);
  return hash.toString(CryptoJS.enc.Hex);
};

// Función para verificar HMAC
export const verifyHMAC = (message: string, providedHash: string): boolean => {
  const expectedHash = generateHMAC(message);
  return expectedHash.toLowerCase() === providedHash.toLowerCase();
};

// Función para generar clave de activación basada en UUID
export const generateActivationKey = (uuid: string): string => {
  if (!uuid || uuid.trim() === '') {
    throw new Error('UUID no puede estar vacío');
  }
  
  // Normalizar UUID (remover guiones y convertir a mayúsculas)
  const normalizedUUID = uuid.replace(/-/g, '').toUpperCase();
  
  // Generar HMAC del UUID
  const hmac = generateHMAC(normalizedUUID);
  
  // Tomar los primeros 16 caracteres y formatear como clave
  const keyPart = hmac.substring(0, 16).toUpperCase();
  
  // Formatear como XXXX-XXXX-XXXX-XXXX
  return `${keyPart.substring(0, 4)}-${keyPart.substring(4, 8)}-${keyPart.substring(8, 12)}-${keyPart.substring(12, 16)}`;
};

// Función para validar clave de activación
export const validateActivationKey = (uuid: string, activationKey: string): boolean => {
  try {
    if (!uuid || !activationKey) {
      return false;
    }
    
    // Generar la clave esperada para este UUID
    const expectedKey = generateActivationKey(uuid);
    
    // Comparar claves (sin importar mayúsculas/minúsculas)
    return expectedKey.toLowerCase() === activationKey.toLowerCase();
  } catch (error) {
    console.error('Error validando clave de activación:', error);
    return false;
  }
};

// Función para obtener UUID del sistema de forma consistente
export const getSystemUUID = async (): Promise<string> => {
  try {
    // Método 1: Verificar si hay un UUID almacenado previamente
    const storedUUID = localStorage.getItem('system_uuid_cache');
    if (storedUUID && storedUUID.length === 36) {
      return storedUUID;
    }

    // Método 2: Usar la API de Electron si está disponible
    if ((window as any).electronAPI) {
      const electronUUID = await (window as any).electronAPI.getSystemUUID();
      if (electronUUID) {
        localStorage.setItem('system_uuid_cache', electronUUID);
        return electronUUID;
      }
    }

    // Método 3: Para aplicaciones web, generar UUID consistente basado en hardware
    const consistentUUID = generateConsistentUUID();

    // Almacenar para uso futuro
    localStorage.setItem('system_uuid_cache', consistentUUID);

    return consistentUUID;

  } catch (error) {
    console.error('Error obteniendo UUID del sistema:', error);

    // Fallback: usar UUID consistente
    const fallbackUUID = generateConsistentUUID();
    localStorage.setItem('system_uuid_cache', fallbackUUID);
    return fallbackUUID;
  }
};

// Función para generar un UUID consistente basado en características del sistema
export const generateConsistentUUID = (): string => {
  // Crear un identificador basado en características del navegador/sistema
  const fingerprint = [
    navigator.userAgent,
    navigator.language,
    screen.width,
    screen.height,
    screen.colorDepth,
    new Date().getTimezoneOffset(),
    navigator.platform,
    navigator.hardwareConcurrency || 4
  ].join('|');

  // Generar hash simple del fingerprint
  let hash = 0;
  for (let i = 0; i < fingerprint.length; i++) {
    const char = fingerprint.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convertir a 32bit integer
  }

  // Convertir hash a UUID format
  const hex = Math.abs(hash).toString(16).padStart(8, '0');
  const uuid = `${hex.substring(0, 8)}-${hex.substring(0, 4)}-4${hex.substring(1, 4)}-8${hex.substring(2, 5)}-${hex}${hex.substring(0, 4)}`;

  return uuid.toUpperCase();
};

// Función para generar un UUID de prueba (solo para desarrollo)
export const generateTestUUID = (): string => {
  // En desarrollo, usar UUID consistente basado en el sistema
  return generateConsistentUUID();
};
