import CryptoJS from 'crypto-js';

// Clave secreta ofuscada - no usar 'e' directamente
const getSecretKey = (): string => {
  // Construir la clave de forma ofuscada
  const parts = ['Lucas2D', 'r', 'pr', 'dador2025'];
  const vowel = String.fromCharCode(101); // 'e'
  return parts[0] + vowel + parts[1] + vowel + parts[2] + vowel + parts[3];
};

// Función para generar HMAC SHA256
export const generateHMAC = (message: string): string => {
  const secretKey = getSecretKey();
  const hash = CryptoJS.HmacSHA256(message, secretKey);
  return hash.toString(CryptoJS.enc.Hex);
};

// Función para verificar HMAC
export const verifyHMAC = (message: string, providedHash: string): boolean => {
  const expectedHash = generateHMAC(message);
  return expectedHash.toLowerCase() === providedHash.toLowerCase();
};

// Función para generar clave de activación basada en UUID
export const generateActivationKey = (uuid: string): string => {
  if (!uuid || uuid.trim() === '') {
    throw new Error('UUID no puede estar vacío');
  }
  
  // Normalizar UUID (remover guiones y convertir a mayúsculas)
  const normalizedUUID = uuid.replace(/-/g, '').toUpperCase();
  
  // Generar HMAC del UUID
  const hmac = generateHMAC(normalizedUUID);
  
  // Tomar los primeros 16 caracteres y formatear como clave
  const keyPart = hmac.substring(0, 16).toUpperCase();
  
  // Formatear como XXXX-XXXX-XXXX-XXXX
  return `${keyPart.substring(0, 4)}-${keyPart.substring(4, 8)}-${keyPart.substring(8, 12)}-${keyPart.substring(12, 16)}`;
};

// Función para validar clave de activación
export const validateActivationKey = (uuid: string, activationKey: string): boolean => {
  try {
    if (!uuid || !activationKey) {
      return false;
    }
    
    // Generar la clave esperada para este UUID
    const expectedKey = generateActivationKey(uuid);
    
    // Comparar claves (sin importar mayúsculas/minúsculas)
    return expectedKey.toLowerCase() === activationKey.toLowerCase();
  } catch (error) {
    console.error('Error validando clave de activación:', error);
    return false;
  }
};

// Función para obtener UUID del sistema (usando PowerShell oculto)
export const getSystemUUID = async (): Promise<string> => {
  try {
    // Verificar si estamos en un entorno que soporta PowerShell
    if (typeof window === 'undefined' || !window.navigator) {
      throw new Error('Entorno no soportado');
    }

    // Usar la API de Node.js si está disponible (Electron)
    if ((window as any).electronAPI) {
      return await (window as any).electronAPI.getSystemUUID();
    }

    // Para aplicaciones web, necesitamos una implementación alternativa
    // ya que no podemos ejecutar PowerShell directamente desde el navegador
    throw new Error('No se puede obtener UUID en entorno web');
    
  } catch (error) {
    console.error('Error obteniendo UUID del sistema:', error);
    throw error;
  }
};

// Función para generar un UUID de prueba (solo para desarrollo)
export const generateTestUUID = (): string => {
  return 'XXXXXXXX-XXXX-4XXX-YXXX-XXXXXXXXXXXX'.replace(/[XY]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'X' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  }).toUpperCase();
};
