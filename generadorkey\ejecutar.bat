@echo off
title Generador de Claves - Battle Strategy Creator
color 0A

echo.
echo ========================================
echo   GENERADOR DE CLAVES DE ACTIVACION
echo   Battle Strategy Creator
echo ========================================
echo.

REM Verificar si Node.js esta instalado
node --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Node.js no esta instalado.
    echo Por favor instala Node.js desde https://nodejs.org/
    echo.
    pause
    exit /b 1
)

REM Verificar si las dependencias estan instaladas
if not exist "node_modules" (
    echo Instalando dependencias...
    echo.
    npm install
    if errorlevel 1 (
        echo ERROR: No se pudieron instalar las dependencias.
        echo.
        pause
        exit /b 1
    )
)

:MENU
cls
echo.
echo ========================================
echo   GENERADOR DE CLAVES DE ACTIVACION
echo   Battle Strategy Creator
echo ========================================
echo.
echo Selecciona una opcion:
echo.
echo 1. Generar clave (Modo Rapido)
echo 2. Interfaz Interactiva Completa
echo 3. Generar desde linea de comandos
echo 4. Ejecutar pruebas del sistema
echo 5. Ayuda y documentacion
echo 6. Salir
echo.
set /p opcion="Ingresa tu opcion (1-6): "

if "%opcion%"=="1" goto MODO_RAPIDO
if "%opcion%"=="2" goto INTERFAZ_COMPLETA
if "%opcion%"=="3" goto LINEA_COMANDOS
if "%opcion%"=="4" goto EJECUTAR_PRUEBAS
if "%opcion%"=="5" goto AYUDA
if "%opcion%"=="6" goto SALIR

echo Opcion invalida. Intenta de nuevo.
pause
goto MENU

:MODO_RAPIDO
cls
echo.
echo ========================================
echo   MODO RAPIDO - GENERAR CLAVE
echo ========================================
echo.
npm start
pause
goto MENU

:INTERFAZ_COMPLETA
cls
echo.
echo ========================================
echo   INTERFAZ INTERACTIVA COMPLETA
echo ========================================
echo.
npm run interfaz
pause
goto MENU

:LINEA_COMANDOS
cls
echo.
echo ========================================
echo   GENERACION DESDE LINEA DE COMANDOS
echo ========================================
echo.
set /p uuid="Ingresa el UUID del sistema: "
set /p secretkey="Ingresa la clave secreta: "

if "%uuid%"=="" (
    echo ERROR: Debes ingresar un UUID.
    pause
    goto MENU
)

if "%secretkey%"=="" (
    echo ERROR: Debes ingresar una clave secreta.
    pause
    goto MENU
)

echo.
echo Generando clave...
echo.
npm run generar "%uuid%" "%secretkey%"
echo.
pause
goto MENU

:EJECUTAR_PRUEBAS
cls
echo.
echo ========================================
echo   EJECUTAR PRUEBAS DEL SISTEMA
echo ========================================
echo.
echo Ejecutando suite de pruebas...
echo.
npm run test
echo.
pause
goto MENU

:AYUDA
cls
echo.
echo ========================================
echo   AYUDA Y DOCUMENTACION
echo ========================================
echo.
echo COMO USAR EL GENERADOR:
echo.
echo 1. El usuario ejecuta Battle Strategy Creator
echo 2. La aplicacion muestra el UUID de su sistema
echo 3. El usuario te envia ese UUID
echo 4. Tu usas este generador para crear la clave
echo 5. Le proporcionas la clave al usuario
echo 6. El usuario activa su aplicacion
echo.
echo FORMATOS ACEPTADOS:
echo - UUID: 12345678-1234-1234-1234-123456789012
echo - UUID: 12345678123412341234123456789012 (sin guiones)
echo - Clave: XXXX-XXXX-XXXX-XXXX (16 digitos)
echo.
echo EJEMPLO:
echo UUID: 12345678-1234-1234-1234-123456789012
echo Clave Secreta: Lucas2Derepredador2025
echo Resultado: 9511-10EB-3B6E-F379
echo.
echo COMANDOS DISPONIBLES:
echo - npm start          (Menu principal)
echo - npm run interfaz   (Interfaz completa)
echo - npm run generar    (Linea de comandos)
echo - npm run test       (Ejecutar pruebas)
echo.
echo SEGURIDAD:
echo - Manten la clave secreta segura
echo - No compartas este generador con usuarios
echo - Cada UUID genera una clave unica
echo.
pause
goto MENU

:SALIR
cls
echo.
echo ========================================
echo   GENERADOR DE CLAVES
echo ========================================
echo.
echo Gracias por usar el Generador de Claves!
echo.
echo Manten tu aplicacion segura.
echo.
timeout /t 3 >nul
exit /b 0
