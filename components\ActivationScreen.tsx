import React, { useState, useEffect } from 'react';
import { validateActivationKey, getSystemUUID, getSystemMACAddress } from '../utils/crypto';
import { getProtectionConfig, ERROR_MESSAGES, SUCCESS_MESSAGES, SECURITY_CONSTANTS } from '../config/protection';

interface ActivationScreenProps {
  onActivationSuccess: () => void;
}

const ActivationScreen: React.FC<ActivationScreenProps> = ({ onActivationSuccess }) => {
  const config = getProtectionConfig();
  const [uuid, setUuid] = useState<string>('');
  const [macAddress, setMacAddress] = useState<string>('');
  const [activationKey, setActivationKey] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string>('');
  const [isValidating, setIsValidating] = useState<boolean>(false);
  const [attempts, setAttempts] = useState<number>(0);
  const [macStatus, setMacStatus] = useState<{
    isReal: boolean;
    source: string;
    message: string;
  } | null>(null);

  useEffect(() => {
    // Obtener UUID y MAC Address del sistema al cargar el componente
    getSystemIdentifiers();
  }, []);

  const getSystemIdentifiers = async () => {
    try {
      setIsLoading(true);
      setError('');

      // Obtener MAC Address real del sistema
      const systemMAC = await getSystemMACAddress();
      setMacAddress(systemMAC);

      // Obtener UUID basado en MAC Address
      const systemUUID = await getSystemUUID();
      setUuid(systemUUID);

      // Determinar estado de la MAC Address
      const isRealMAC = localStorage.getItem('system_mac_is_real') === 'true';
      const macSource = localStorage.getItem('system_mac_source') || 'Unknown';

      setMacStatus({
        isReal: isRealMAC,
        source: macSource,
        message: isRealMAC
          ? 'MAC Address real obtenida del hardware'
          : 'MAC Address generada de forma consistente'
      });

    } catch (err) {
      setError('Error al obtener identificadores del sistema');
      console.error('Error:', err);
    } finally {
      setIsLoading(false);
    }
  };

  // Simular la obtención del UUID usando PowerShell
  const simulateGetSystemUUID = async (): Promise<string> => {
    return new Promise((resolve, reject) => {
      // Simular delay de ejecución de PowerShell
      setTimeout(() => {
        try {
          // En una aplicación real, aquí ejecutarías:
          // Get-WmiObject -Class Win32_ComputerSystemProduct | Select-Object -Property UUID
          
          // Para la demostración, generar un UUID de prueba
          const testUUID = generateTestUUID();
          resolve(testUUID);
        } catch (error) {
          reject(error);
        }
      }, 1000);
    });
  };

  const handleActivationKeyChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    let value = e.target.value.toUpperCase();
    
    // Remover caracteres no válidos
    value = value.replace(/[^A-F0-9-]/g, '');
    
    // Formatear automáticamente con guiones
    if (value.length > 0) {
      value = value.replace(/-/g, ''); // Remover guiones existentes
      const formatted = value.match(/.{1,4}/g)?.join('-') || value;
      if (formatted.length <= 19) { // XXXX-XXXX-XXXX-XXXX = 19 caracteres
        value = formatted;
      }
    }
    
    setActivationKey(value);
    setError('');
  };

  const handleActivate = async () => {
    // Verificar límite de intentos
    const storedAttempts = parseInt(localStorage.getItem(config.storageKeys.attempts) || '0');
    if (storedAttempts >= config.maxActivationAttempts) {
      setError(ERROR_MESSAGES.MAX_ATTEMPTS_REACHED);
      return;
    }

    if (!uuid || !activationKey) {
      setError(ERROR_MESSAGES.EMPTY_FIELDS);
      return;
    }

    if (!SECURITY_CONSTANTS.KEY_FORMAT_REGEX.test(activationKey)) {
      setError(ERROR_MESSAGES.INVALID_KEY_FORMAT);
      return;
    }

    setIsValidating(true);
    setError('');

    try {
      // Incrementar contador de intentos
      const newAttempts = storedAttempts + 1;
      localStorage.setItem(config.storageKeys.attempts, newAttempts.toString());
      localStorage.setItem(config.storageKeys.lastAttempt, Date.now().toString());
      setAttempts(newAttempts);

      // Simular delay de validación
      await new Promise(resolve => setTimeout(resolve, 500));

      const isValid = validateActivationKey(uuid, activationKey);

      if (isValid) {
        // Limpiar intentos fallidos
        localStorage.removeItem(config.storageKeys.attempts);
        localStorage.removeItem(config.storageKeys.lastAttempt);

        // Guardar estado de activación en localStorage
        localStorage.setItem(config.storageKeys.activated, 'true');
        localStorage.setItem(config.storageKeys.uuid, uuid);
        onActivationSuccess();
      } else {
        if (newAttempts >= config.maxActivationAttempts) {
          setError(ERROR_MESSAGES.MAX_ATTEMPTS_REACHED);
        } else {
          setError(`${ERROR_MESSAGES.INVALID_KEY} (Intento ${newAttempts}/${config.maxActivationAttempts})`);
        }
      }
    } catch (err) {
      setError(ERROR_MESSAGES.SYSTEM_ERROR);
      console.error('Error:', err);
    } finally {
      setIsValidating(false);
    }
  };

  const copyUUIDToClipboard = () => {
    navigator.clipboard.writeText(uuid).then(() => {
      // Mostrar feedback visual (opcional)
    }).catch(err => {
      console.error('Error al copiar UUID:', err);
    });
  };

  const copyMACToClipboard = () => {
    navigator.clipboard.writeText(macAddress).then(() => {
      // Mostrar feedback visual (opcional)
    }).catch(err => {
      console.error('Error al copiar MAC Address:', err);
    });
  };

  if (isLoading) {
    return (
      <div className="fixed inset-0 bg-gray-900 flex items-center justify-center">
        <div className="bg-gray-800 p-8 rounded-lg shadow-xl max-w-md w-full mx-4">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
            <h2 className="text-xl font-bold text-white mb-2">Inicializando Sistema</h2>
            <p className="text-gray-300">Obteniendo información del sistema...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-gray-900 flex items-center justify-center">
      <div className="bg-gray-800 p-8 rounded-lg shadow-xl max-w-md w-full mx-4">
        <div className="text-center mb-6">
          <h1 className="text-2xl font-bold text-white mb-2">Battle Strategy Creator</h1>
          <p className="text-gray-300">Verificación de Licencia Requerida</p>
          <div className="mt-2 text-sm text-gray-400">
            <p>🛡️ Sistema protegido con HMAC SHA256</p>
            <p>🔑 Activación basada en MAC Address del hardware</p>
          </div>
        </div>

        <div className="space-y-4">
          {/* MAC Address del Sistema */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              MAC Address del Sistema:
            </label>
            <div className="flex">
              <input
                type="text"
                value={macAddress}
                readOnly
                className="flex-1 px-3 py-2 bg-gray-700 border border-gray-600 rounded-l-md text-white font-mono text-sm"
              />
              <button
                onClick={copyMACToClipboard}
                className="px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-r-md text-sm"
                title="Copiar MAC Address"
              >
                📋
              </button>
            </div>
            <p className="text-xs text-gray-400 mt-1">
              📧 Envía esta MAC Address al administrador para obtener tu clave de activación
            </p>

            {/* Estado de la MAC Address */}
            {macStatus && (
              <div className={`mt-2 p-2 border rounded text-xs ${
                macStatus.isReal
                  ? 'bg-green-900 border-green-700 text-green-200'
                  : 'bg-orange-900 border-orange-700 text-orange-200'
              }`}>
                <p className="font-semibold">
                  {macStatus.isReal ? '✅ MAC Address Real del Hardware' : '⚠️ MAC Address Generada'}
                </p>
                <p>{macStatus.message}</p>
                <p className="text-xs mt-1">Fuente: {macStatus.source}</p>
                {!macStatus.isReal && (
                  <div className="mt-2 p-2 bg-red-900 border border-red-700 rounded">
                    <p className="font-semibold text-red-200">🚨 IMPORTANTE: Para obtener MAC REAL:</p>
                    <p className="text-red-300">1. Ejecuta "ejecutar-admin.bat" como administrador</p>
                    <p className="text-red-300">2. Luego recarga esta página</p>
                    <p className="text-red-300 mt-1 font-semibold">⚠️ Sin MAC real, la activación puede no funcionar correctamente</p>
                  </div>
                )}
                {macStatus.isReal && (
                  <div className="mt-2">
                    <p className="text-green-300">🎉 Perfecto! Esta es la MAC real de tu adaptador de red</p>
                    <p className="text-green-300">📋 Úsala para generar tu clave de activación</p>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* UUID del Sistema (para referencia) */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              UUID del Sistema (basado en MAC):
            </label>
            <div className="flex">
              <input
                type="text"
                value={uuid}
                readOnly
                className="flex-1 px-3 py-2 bg-gray-700 border border-gray-600 rounded-l-md text-white font-mono text-sm"
              />
              <button
                onClick={copyUUIDToClipboard}
                className="px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-r-md text-sm"
                title="Copiar UUID"
              >
                📋
              </button>
            </div>
            <p className="text-xs text-gray-400 mt-1">
              📋 UUID generado automáticamente basado en la MAC Address
            </p>
            <div className="mt-2 p-2 bg-blue-900 border border-blue-700 rounded text-xs text-blue-200">
              <p className="font-semibold">📋 Proceso de activación:</p>
              <p>1. Copia la MAC Address de arriba</p>
              <p>2. Genera tu clave: cd generadorkey && npm run generar "TU-MAC" "Lucas2Derepredador2025"</p>
              <p>3. Introduce la clave generada abajo</p>
              <p>4. Presiona "Activar"</p>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Clave de Activación:
            </label>
            <input
              type="text"
              value={activationKey}
              onChange={handleActivationKeyChange}
              placeholder="XXXX-XXXX-XXXX-XXXX"
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white font-mono text-center"
              maxLength={19}
            />
          </div>

          {error && (
            <div className="bg-red-900 border border-red-700 text-red-300 px-4 py-3 rounded">
              {error}
            </div>
          )}

          <button
            onClick={handleActivate}
            disabled={isValidating || !uuid || !activationKey}
            className="w-full bg-green-600 hover:bg-green-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white font-bold py-2 px-4 rounded transition-colors"
          >
            {isValidating ? 'Validando...' : 'Activar'}
          </button>
        </div>

        <div className="mt-6 text-center">
          <p className="text-xs text-gray-400">
            © 2025 Battle Strategy Creator - Protegido por HMAC SHA256
          </p>
        </div>
      </div>
    </div>
  );
};

export default ActivationScreen;
