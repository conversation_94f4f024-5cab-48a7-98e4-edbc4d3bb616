import React, { useState, useEffect } from 'react';
import { validateActivationKey, getSystemMACAddress } from '../utils/crypto';
import { getProtectionConfig, ERROR_MESSAGES, SUCCESS_MESSAGES, SECURITY_CONSTANTS } from '../config/protection';

interface ActivationScreenProps {
  onActivationSuccess: () => void;
}

const ActivationScreen: React.FC<ActivationScreenProps> = ({ onActivationSuccess }) => {
  const config = getProtectionConfig();
  const [macAddress, setMacAddress] = useState<string>('');
  const [activationKey, setActivationKey] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string>('');
  const [isValidating, setIsValidating] = useState<boolean>(false);
  const [attempts, setAttempts] = useState<number>(0);
  const [macStatus, setMacStatus] = useState<{
    isReal: boolean;
    source: string;
    message: string;
    adapterName?: string;
    description?: string;
  } | null>(null);

  useEffect(() => {
    // Obtener SOLO MAC Address real del sistema al cargar el componente
    getRealMACAddress();
  }, []);

  const getRealMACAddress = async () => {
    try {
      setIsLoading(true);
      setError('');

      // Obtener SOLO MAC Address real del sistema
      const systemMAC = await getSystemMACAddress();
      setMacAddress(systemMAC);

      // Leer información adicional del archivo JSON si está disponible
      try {
        const response = await fetch('/mac_address.json');
        if (response.ok) {
          const macData = await response.json();
          setMacStatus({
            isReal: true,
            source: macData.method || 'Hardware Real',
            message: 'MAC Address real obtenida del hardware',
            adapterName: macData.adapterName,
            description: macData.description
          });
        } else {
          // Si no hay archivo JSON, usar información básica
          setMacStatus({
            isReal: true,
            source: 'Hardware Real',
            message: 'MAC Address real obtenida del hardware'
          });
        }
      } catch (jsonError) {
        // Si no se puede leer el JSON, asumir que es real si llegamos aquí
        setMacStatus({
          isReal: true,
          source: 'Hardware Real',
          message: 'MAC Address real obtenida del hardware'
        });
      }

    } catch (err) {
      setError('No se pudo obtener MAC Address real. Ejecuta "ejecutar-admin.bat" como administrador.');
      console.error('Error:', err);

      // No hay fallback - mostrar error
      setMacStatus({
        isReal: false,
        source: 'Error',
        message: 'No se pudo obtener MAC Address real del hardware'
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Simular la obtención del UUID usando PowerShell
  const simulateGetSystemUUID = async (): Promise<string> => {
    return new Promise((resolve, reject) => {
      // Simular delay de ejecución de PowerShell
      setTimeout(() => {
        try {
          // En una aplicación real, aquí ejecutarías:
          // Get-WmiObject -Class Win32_ComputerSystemProduct | Select-Object -Property UUID
          
          // Para la demostración, generar un UUID de prueba
          const testUUID = generateTestUUID();
          resolve(testUUID);
        } catch (error) {
          reject(error);
        }
      }, 1000);
    });
  };

  const handleActivationKeyChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    let value = e.target.value.toUpperCase();
    
    // Remover caracteres no válidos
    value = value.replace(/[^A-F0-9-]/g, '');
    
    // Formatear automáticamente con guiones
    if (value.length > 0) {
      value = value.replace(/-/g, ''); // Remover guiones existentes
      const formatted = value.match(/.{1,4}/g)?.join('-') || value;
      if (formatted.length <= 19) { // XXXX-XXXX-XXXX-XXXX = 19 caracteres
        value = formatted;
      }
    }
    
    setActivationKey(value);
    setError('');
  };

  const handleActivate = async () => {
    // Verificar límite de intentos
    const storedAttempts = parseInt(localStorage.getItem(config.storageKeys.attempts) || '0');
    if (storedAttempts >= config.maxActivationAttempts) {
      setError(ERROR_MESSAGES.MAX_ATTEMPTS_REACHED);
      return;
    }

    if (!macAddress || !activationKey) {
      setError('Por favor, introduce una clave de activación y asegúrate de que se haya obtenido la MAC Address.');
      return;
    }

    if (!SECURITY_CONSTANTS.KEY_FORMAT_REGEX.test(activationKey)) {
      setError(ERROR_MESSAGES.INVALID_KEY_FORMAT);
      return;
    }

    setIsValidating(true);
    setError('');

    try {
      // Incrementar contador de intentos
      const newAttempts = storedAttempts + 1;
      localStorage.setItem(config.storageKeys.attempts, newAttempts.toString());
      localStorage.setItem(config.storageKeys.lastAttempt, Date.now().toString());
      setAttempts(newAttempts);

      // Simular delay de validación
      await new Promise(resolve => setTimeout(resolve, 500));

      const isValid = validateActivationKey(macAddress, activationKey);

      if (isValid) {
        // Limpiar intentos fallidos
        localStorage.removeItem(config.storageKeys.attempts);
        localStorage.removeItem(config.storageKeys.lastAttempt);

        // Guardar estado de activación en localStorage usando MAC Address
        localStorage.setItem(config.storageKeys.activated, 'true');
        localStorage.setItem(config.storageKeys.uuid, macAddress); // Guardar MAC en lugar de UUID
        onActivationSuccess();
      } else {
        if (newAttempts >= config.maxActivationAttempts) {
          setError(ERROR_MESSAGES.MAX_ATTEMPTS_REACHED);
        } else {
          setError(`${ERROR_MESSAGES.INVALID_KEY} (Intento ${newAttempts}/${config.maxActivationAttempts})`);
        }
      }
    } catch (err) {
      setError(ERROR_MESSAGES.SYSTEM_ERROR);
      console.error('Error:', err);
    } finally {
      setIsValidating(false);
    }
  };

  // ELIMINADO: Ya no usamos UUID

  const copyMACToClipboard = () => {
    navigator.clipboard.writeText(macAddress).then(() => {
      // Mostrar feedback visual (opcional)
    }).catch(err => {
      console.error('Error al copiar MAC Address:', err);
    });
  };

  if (isLoading) {
    return (
      <div className="fixed inset-0 bg-gray-900 flex items-center justify-center">
        <div className="bg-gray-800 p-8 rounded-lg shadow-xl max-w-md w-full mx-4">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
            <h2 className="text-xl font-bold text-white mb-2">Inicializando Sistema</h2>
            <p className="text-gray-300">Obteniendo información del sistema...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-gray-900 flex items-center justify-center">
      <div className="bg-gray-800 p-8 rounded-lg shadow-xl max-w-md w-full mx-4">
        <div className="text-center mb-6">
          <h1 className="text-2xl font-bold text-white mb-2">Battle Strategy Creator</h1>
          <p className="text-gray-300">Verificación de Licencia Requerida</p>
          <div className="mt-2 text-sm text-gray-400">
            <p>🛡️ Sistema protegido con HMAC SHA256</p>
            <p>🔗 Activación basada ÚNICAMENTE en MAC Address real del hardware</p>
          </div>
        </div>

        <div className="space-y-4">
          {/* MAC Address del Sistema */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              MAC Address del Sistema:
            </label>
            <div className="flex">
              <input
                type="text"
                value={macAddress}
                readOnly
                className="flex-1 px-3 py-2 bg-gray-700 border border-gray-600 rounded-l-md text-white font-mono text-sm"
              />
              <button
                onClick={copyMACToClipboard}
                className="px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-r-md text-sm"
                title="Copiar MAC Address"
              >
                📋
              </button>
            </div>
            <p className="text-xs text-gray-400 mt-1">
              📧 Envía esta MAC Address al administrador para obtener tu clave de activación
            </p>

            {/* Estado de la MAC Address */}
            {macStatus && (
              <div className={`mt-2 p-2 border rounded text-xs ${
                macStatus.isReal
                  ? 'bg-green-900 border-green-700 text-green-200'
                  : 'bg-red-900 border-red-700 text-red-200'
              }`}>
                <p className="font-semibold">
                  {macStatus.isReal ? '✅ MAC Address Real del Hardware' : '❌ Error: No se pudo obtener MAC real'}
                </p>
                <p>{macStatus.message}</p>
                <p className="text-xs mt-1">Método: {macStatus.source}</p>
                {macStatus.adapterName && (
                  <p className="text-xs">Adaptador: {macStatus.adapterName}</p>
                )}
                {macStatus.description && (
                  <p className="text-xs">Descripción: {macStatus.description}</p>
                )}

                {!macStatus.isReal && (
                  <div className="mt-2 p-2 bg-red-800 border border-red-600 rounded">
                    <p className="font-semibold text-red-100">🚨 REQUERIDO: Obtener MAC Address real</p>
                    <p className="text-red-200">1. Ejecuta "ejecutar-admin.bat" como administrador</p>
                    <p className="text-red-200">2. Luego recarga esta página</p>
                    <p className="text-red-200 mt-1 font-semibold">⚠️ Sin MAC real, NO se puede activar la aplicación</p>
                  </div>
                )}

                {macStatus.isReal && (
                  <div className="mt-2 p-2 bg-green-800 border border-green-600 rounded">
                    <p className="text-green-100 font-semibold">🎉 ¡Excelente! MAC Address real detectada</p>
                    <p className="text-green-200">📋 Esta MAC es única de tu adaptador de red</p>
                    <p className="text-green-200">🔑 Úsala para generar tu clave de activación</p>
                    <p className="text-green-200">✅ La activación será permanente y estable</p>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* ELIMINADO: Ya no usamos UUID del sistema */}
            <div className="mt-2 p-2 bg-blue-900 border border-blue-700 rounded text-xs text-blue-200">
              <p className="font-semibold">📋 Proceso de activación simplificado:</p>
              <p>1. 📋 Copia la MAC Address real de arriba (botón copiar)</p>
              <p>2. 🔑 Genera tu clave: cd generadorkey</p>
              <p>3. 🔑 npm run generar "TU-MAC-REAL" "Lucas2Derepredador2025"</p>
              <p>4. ✅ Introduce la clave generada abajo y presiona "Activar"</p>
              <p className="mt-1 font-semibold text-blue-100">💡 Solo funciona con MAC Address REAL del hardware</p>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Clave de Activación:
            </label>
            <input
              type="text"
              value={activationKey}
              onChange={handleActivationKeyChange}
              placeholder="XXXX-XXXX-XXXX-XXXX"
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white font-mono text-center"
              maxLength={19}
            />
          </div>

          {error && (
            <div className="bg-red-900 border border-red-700 text-red-300 px-4 py-3 rounded">
              {error}
            </div>
          )}

          <button
            onClick={handleActivate}
            disabled={isValidating || !uuid || !activationKey}
            className="w-full bg-green-600 hover:bg-green-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white font-bold py-2 px-4 rounded transition-colors"
          >
            {isValidating ? 'Validando...' : 'Activar'}
          </button>
        </div>

        <div className="mt-6 text-center">
          <p className="text-xs text-gray-400">
            © 2025 Battle Strategy Creator - Protegido por HMAC SHA256
          </p>
        </div>
      </div>
    </div>
  );
};

export default ActivationScreen;
