import React, { useState, useEffect } from 'react';
import { validateActivationKey, generateTestUUID } from '../utils/crypto';
import { getProtectionConfig, ERROR_MESSAGES, SUCCESS_MESSAGES, SECURITY_CONSTANTS } from '../config/protection';

interface ActivationScreenProps {
  onActivationSuccess: () => void;
}

const ActivationScreen: React.FC<ActivationScreenProps> = ({ onActivationSuccess }) => {
  const config = getProtectionConfig();
  const [uuid, setUuid] = useState<string>('');
  const [activationKey, setActivationKey] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string>('');
  const [isValidating, setIsValidating] = useState<boolean>(false);
  const [attempts, setAttempts] = useState<number>(0);

  useEffect(() => {
    // Obtener UUID del sistema al cargar el componente
    getSystemUUID();
  }, []);

  const getSystemUUID = async () => {
    try {
      setIsLoading(true);
      setError('');
      
      // Simular la obtención del UUID del sistema
      // En una aplicación real, esto ejecutaría PowerShell
      const systemUUID = await simulateGetSystemUUID();
      setUuid(systemUUID);
    } catch (err) {
      setError('Error al obtener el UUID del sistema');
      console.error('Error:', err);
    } finally {
      setIsLoading(false);
    }
  };

  // Simular la obtención del UUID usando PowerShell
  const simulateGetSystemUUID = async (): Promise<string> => {
    return new Promise((resolve, reject) => {
      // Simular delay de ejecución de PowerShell
      setTimeout(() => {
        try {
          // En una aplicación real, aquí ejecutarías:
          // Get-WmiObject -Class Win32_ComputerSystemProduct | Select-Object -Property UUID
          
          // Para la demostración, generar un UUID de prueba
          const testUUID = generateTestUUID();
          resolve(testUUID);
        } catch (error) {
          reject(error);
        }
      }, 1000);
    });
  };

  const handleActivationKeyChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    let value = e.target.value.toUpperCase();
    
    // Remover caracteres no válidos
    value = value.replace(/[^A-F0-9-]/g, '');
    
    // Formatear automáticamente con guiones
    if (value.length > 0) {
      value = value.replace(/-/g, ''); // Remover guiones existentes
      const formatted = value.match(/.{1,4}/g)?.join('-') || value;
      if (formatted.length <= 19) { // XXXX-XXXX-XXXX-XXXX = 19 caracteres
        value = formatted;
      }
    }
    
    setActivationKey(value);
    setError('');
  };

  const handleActivate = async () => {
    // Verificar límite de intentos
    const storedAttempts = parseInt(localStorage.getItem(config.storageKeys.attempts) || '0');
    if (storedAttempts >= config.maxActivationAttempts) {
      setError(ERROR_MESSAGES.MAX_ATTEMPTS_REACHED);
      return;
    }

    if (!uuid || !activationKey) {
      setError(ERROR_MESSAGES.EMPTY_FIELDS);
      return;
    }

    if (!SECURITY_CONSTANTS.KEY_FORMAT_REGEX.test(activationKey)) {
      setError(ERROR_MESSAGES.INVALID_KEY_FORMAT);
      return;
    }

    setIsValidating(true);
    setError('');

    try {
      // Incrementar contador de intentos
      const newAttempts = storedAttempts + 1;
      localStorage.setItem(config.storageKeys.attempts, newAttempts.toString());
      localStorage.setItem(config.storageKeys.lastAttempt, Date.now().toString());
      setAttempts(newAttempts);

      // Simular delay de validación
      await new Promise(resolve => setTimeout(resolve, 500));

      const isValid = validateActivationKey(uuid, activationKey);

      if (isValid) {
        // Limpiar intentos fallidos
        localStorage.removeItem(config.storageKeys.attempts);
        localStorage.removeItem(config.storageKeys.lastAttempt);

        // Guardar estado de activación en localStorage
        localStorage.setItem(config.storageKeys.activated, 'true');
        localStorage.setItem(config.storageKeys.uuid, uuid);
        onActivationSuccess();
      } else {
        if (newAttempts >= config.maxActivationAttempts) {
          setError(ERROR_MESSAGES.MAX_ATTEMPTS_REACHED);
        } else {
          setError(`${ERROR_MESSAGES.INVALID_KEY} (Intento ${newAttempts}/${config.maxActivationAttempts})`);
        }
      }
    } catch (err) {
      setError(ERROR_MESSAGES.SYSTEM_ERROR);
      console.error('Error:', err);
    } finally {
      setIsValidating(false);
    }
  };

  const copyUUIDToClipboard = () => {
    navigator.clipboard.writeText(uuid).then(() => {
      // Mostrar feedback visual (opcional)
    }).catch(err => {
      console.error('Error al copiar UUID:', err);
    });
  };

  if (isLoading) {
    return (
      <div className="fixed inset-0 bg-gray-900 flex items-center justify-center">
        <div className="bg-gray-800 p-8 rounded-lg shadow-xl max-w-md w-full mx-4">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
            <h2 className="text-xl font-bold text-white mb-2">Inicializando Sistema</h2>
            <p className="text-gray-300">Obteniendo información del sistema...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-gray-900 flex items-center justify-center">
      <div className="bg-gray-800 p-8 rounded-lg shadow-xl max-w-md w-full mx-4">
        <div className="text-center mb-6">
          <h1 className="text-2xl font-bold text-white mb-2">Battle Strategy Creator</h1>
          <p className="text-gray-300">Verificación de Licencia Requerida</p>
          <div className="mt-2 text-sm text-gray-400">
            <p>🛡️ Sistema protegido con HMAC SHA256</p>
            <p>🔑 Activación basada en UUID del sistema</p>
          </div>
        </div>

        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              UUID del Sistema:
            </label>
            <div className="flex">
              <input
                type="text"
                value={uuid}
                readOnly
                className="flex-1 px-3 py-2 bg-gray-700 border border-gray-600 rounded-l-md text-white font-mono text-sm"
              />
              <button
                onClick={copyUUIDToClipboard}
                className="px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-r-md text-sm"
                title="Copiar UUID"
              >
                📋
              </button>
            </div>
            <p className="text-xs text-gray-400 mt-1">
              📧 Envía este UUID al administrador para obtener tu clave de activación
            </p>
            <div className="mt-2 p-2 bg-blue-900 border border-blue-700 rounded text-xs text-blue-200">
              <p className="font-semibold">📋 Proceso de activación:</p>
              <p>1. Copia el UUID de arriba</p>
              <p>2. Envíalo al administrador del sistema</p>
              <p>3. Recibe tu clave de activación personalizada</p>
              <p>4. Introdúcela abajo y presiona "Activar"</p>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Clave de Activación:
            </label>
            <input
              type="text"
              value={activationKey}
              onChange={handleActivationKeyChange}
              placeholder="XXXX-XXXX-XXXX-XXXX"
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white font-mono text-center"
              maxLength={19}
            />
          </div>

          {error && (
            <div className="bg-red-900 border border-red-700 text-red-300 px-4 py-3 rounded">
              {error}
            </div>
          )}

          <button
            onClick={handleActivate}
            disabled={isValidating || !uuid || !activationKey}
            className="w-full bg-green-600 hover:bg-green-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white font-bold py-2 px-4 rounded transition-colors"
          >
            {isValidating ? 'Validando...' : 'Activar'}
          </button>
        </div>

        <div className="mt-6 text-center">
          <p className="text-xs text-gray-400">
            © 2025 Battle Strategy Creator - Protegido por HMAC SHA256
          </p>
        </div>
      </div>
    </div>
  );
};

export default ActivationScreen;
