// Script para resetear el estado de activación
// Útil para pruebas y desarrollo

import { getProtectionConfig } from '../config/protection.js';

const resetActivation = () => {
  console.log('🔄 RESETEANDO ESTADO DE ACTIVACIÓN');
  console.log('═'.repeat(50));
  console.log('');
  
  try {
    const config = getProtectionConfig();
    
    // Verificar si estamos en un entorno de navegador
    if (typeof localStorage !== 'undefined') {
      // Limpiar todas las claves relacionadas con activación
      const keysToRemove = [
        config.storageKeys.activated,
        config.storageKeys.uuid,
        config.storageKeys.attempts,
        config.storageKeys.lastAttempt
      ];
      
      console.log('🗑️  Limpiando localStorage...');
      keysToRemove.forEach(key => {
        const value = localStorage.getItem(key);
        if (value) {
          localStorage.removeItem(key);
          console.log(`   ✅ Removido: ${key} = ${value}`);
        } else {
          console.log(`   ⚪ No existe: ${key}`);
        }
      });
      
      console.log('');
      console.log('✅ Estado de activación reseteado');
      console.log('🔄 La aplicación requerirá nueva activación');
      
    } else {
      console.log('⚠️  Este script debe ejecutarse en un entorno de navegador');
      console.log('💡 Alternativa: Abrir DevTools y ejecutar:');
      console.log('');
      console.log('localStorage.removeItem("app_activated");');
      console.log('localStorage.removeItem("activation_uuid");');
      console.log('localStorage.removeItem("activation_attempts");');
      console.log('localStorage.removeItem("last_activation_attempt");');
      console.log('location.reload();');
    }
    
  } catch (error) {
    console.error('❌ Error reseteando activación:', error.message);
  }
  
  console.log('');
  console.log('═'.repeat(50));
};

// Función para mostrar estado actual
const showActivationStatus = () => {
  console.log('📊 ESTADO ACTUAL DE ACTIVACIÓN');
  console.log('═'.repeat(50));
  console.log('');
  
  try {
    const config = getProtectionConfig();
    
    if (typeof localStorage !== 'undefined') {
      const status = {
        activated: localStorage.getItem(config.storageKeys.activated),
        uuid: localStorage.getItem(config.storageKeys.uuid),
        attempts: localStorage.getItem(config.storageKeys.attempts),
        lastAttempt: localStorage.getItem(config.storageKeys.lastAttempt)
      };
      
      console.log('📋 Valores en localStorage:');
      Object.entries(status).forEach(([key, value]) => {
        const icon = value ? '✅' : '❌';
        console.log(`   ${icon} ${key}: ${value || 'No definido'}`);
      });
      
      console.log('');
      if (status.activated === 'true') {
        console.log('🟢 Estado: ACTIVADO');
        console.log(`📱 UUID: ${status.uuid}`);
      } else {
        console.log('🔴 Estado: NO ACTIVADO');
        if (status.attempts) {
          console.log(`🔢 Intentos: ${status.attempts}`);
        }
      }
      
    } else {
      console.log('⚠️  No se puede acceder a localStorage');
    }
    
  } catch (error) {
    console.error('❌ Error verificando estado:', error.message);
  }
  
  console.log('');
  console.log('═'.repeat(50));
};

// Función principal
const main = () => {
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    console.log('');
    console.log('🔧 HERRAMIENTA DE GESTIÓN DE ACTIVACIÓN');
    console.log('═'.repeat(50));
    console.log('');
    console.log('Uso: node resetActivation.js <comando>');
    console.log('');
    console.log('Comandos disponibles:');
    console.log('  reset   - Resetear estado de activación');
    console.log('  status  - Mostrar estado actual');
    console.log('  help    - Mostrar esta ayuda');
    console.log('');
    console.log('Ejemplos:');
    console.log('  node resetActivation.js reset');
    console.log('  node resetActivation.js status');
    console.log('');
    console.log('💡 Para usar en navegador:');
    console.log('  1. Abrir DevTools (F12)');
    console.log('  2. Ir a Console');
    console.log('  3. Ejecutar comandos localStorage');
    console.log('');
    process.exit(1);
  }
  
  const command = args[0].toLowerCase();
  
  switch (command) {
    case 'reset':
      resetActivation();
      break;
    case 'status':
      showActivationStatus();
      break;
    case 'help':
      main(); // Mostrar ayuda
      break;
    default:
      console.log(`❌ Comando desconocido: ${command}`);
      console.log('💡 Usa "help" para ver comandos disponibles');
      process.exit(1);
  }
};

// Ejecutar si es llamado directamente
if (import.meta.url.includes(process.argv[1])) {
  main();
}

export { resetActivation, showActivationStatus };
