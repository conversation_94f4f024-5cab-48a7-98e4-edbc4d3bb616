// Interfaz interactiva para el generador de claves
import readlineSync from 'readline-sync';
import { generarClaveActivacion, validarClaveActivacion, generarInformacionDetallada } from './generador.js';

// Configuración de colores para la consola
const colores = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m'
};

const mostrarTitulo = () => {
  console.clear();
  console.log(colores.cyan + colores.bright);
  console.log('╔══════════════════════════════════════════════════════════════╗');
  console.log('║                 GENERADOR DE CLAVES DE ACTIVACIÓN            ║');
  console.log('║                    Battle Strategy Creator                   ║');
  console.log('╚══════════════════════════════════════════════════════════════╝');
  console.log(colores.reset);
  console.log('');
};

const mostrarMenu = () => {
  console.log(colores.yellow + 'Selecciona una opción:' + colores.reset);
  console.log('');
  console.log('1. 🔑 Generar clave de activación');
  console.log('2. ✅ Validar clave de activación');
  console.log('3. 📊 Información detallada del proceso');
  console.log('4. 📋 Generar múltiples claves');
  console.log('5. ❌ Salir');
  console.log('');
};

const generarClaveInteractiva = () => {
  console.log(colores.green + '🔑 GENERAR CLAVE DE ACTIVACIÓN' + colores.reset);
  console.log('═'.repeat(50));
  console.log('');
  
  // Solicitar UUID
  const uuid = readlineSync.question('📋 Ingresa el UUID del sistema: ', {
    limit: /^[0-9A-Fa-f-]{32,36}$/,
    limitMessage: 'UUID debe tener formato válido (32-36 caracteres hexadecimales)'
  });
  
  // Solicitar clave secreta
  const secretKey = readlineSync.question('🔐 Ingresa la clave secreta: ', {
    hideEchoBack: true,
    mask: '*'
  });
  
  try {
    const clave = generarClaveActivacion(uuid, secretKey);
    
    console.log('');
    console.log(colores.green + '✅ CLAVE GENERADA EXITOSAMENTE:' + colores.reset);
    console.log('');
    console.log(colores.bright + colores.cyan + `🔑 ${clave}` + colores.reset);
    console.log('');
    console.log(colores.yellow + '⚠️  IMPORTANTE: Guarda esta clave de forma segura' + colores.reset);
    
  } catch (error) {
    console.log('');
    console.log(colores.red + '❌ ERROR: ' + error.message + colores.reset);
  }
  
  console.log('');
  readlineSync.question('Presiona ENTER para continuar...');
};

const validarClaveInteractiva = () => {
  console.log(colores.blue + '✅ VALIDAR CLAVE DE ACTIVACIÓN' + colores.reset);
  console.log('═'.repeat(50));
  console.log('');
  
  const uuid = readlineSync.question('📋 Ingresa el UUID del sistema: ');
  const secretKey = readlineSync.question('🔐 Ingresa la clave secreta: ', {
    hideEchoBack: true,
    mask: '*'
  });
  const claveActivacion = readlineSync.question('🔑 Ingresa la clave de activación: ');
  
  try {
    const esValida = validarClaveActivacion(uuid, secretKey, claveActivacion);
    
    console.log('');
    if (esValida) {
      console.log(colores.green + '✅ CLAVE VÁLIDA' + colores.reset);
      console.log('La clave de activación es correcta para este UUID.');
    } else {
      console.log(colores.red + '❌ CLAVE INVÁLIDA' + colores.reset);
      console.log('La clave de activación no coincide con este UUID.');
    }
    
  } catch (error) {
    console.log('');
    console.log(colores.red + '❌ ERROR: ' + error.message + colores.reset);
  }
  
  console.log('');
  readlineSync.question('Presiona ENTER para continuar...');
};

const mostrarInformacionDetallada = () => {
  console.log(colores.magenta + '📊 INFORMACIÓN DETALLADA DEL PROCESO' + colores.reset);
  console.log('═'.repeat(50));
  console.log('');
  
  const uuid = readlineSync.question('📋 Ingresa el UUID del sistema: ');
  const secretKey = readlineSync.question('🔐 Ingresa la clave secreta: ', {
    hideEchoBack: true,
    mask: '*'
  });
  
  try {
    const info = generarInformacionDetallada(uuid, secretKey);
    
    console.log('');
    console.log(colores.cyan + '📋 INFORMACIÓN DETALLADA:' + colores.reset);
    console.log('─'.repeat(50));
    console.log(`UUID Original:        ${info.uuidOriginal}`);
    console.log(`UUID Normalizado:     ${info.uuidNormalizado}`);
    console.log(`Clave Secreta:        ${'*'.repeat(info.claveSecreta.length)}`);
    console.log(`HMAC SHA256:          ${info.hmacCompleto}`);
    console.log(`Primeros 16 dígitos:  ${info.primeros16Digitos}`);
    console.log('');
    console.log(colores.bright + colores.green + `🔑 CLAVE DE ACTIVACIÓN: ${info.claveActivacion}` + colores.reset);
    console.log('');
    console.log('📝 Detalles técnicos:');
    console.log(`   - Longitud HMAC: ${info.longitudHmac} caracteres`);
    console.log(`   - Longitud clave: ${info.longitudClave} caracteres`);
    console.log(`   - Generado: ${info.timestamp}`);
    
  } catch (error) {
    console.log('');
    console.log(colores.red + '❌ ERROR: ' + error.message + colores.reset);
  }
  
  console.log('');
  readlineSync.question('Presiona ENTER para continuar...');
};

const generarMultiplesClaves = () => {
  console.log(colores.yellow + '📋 GENERAR MÚLTIPLES CLAVES' + colores.reset);
  console.log('═'.repeat(50));
  console.log('');
  
  const secretKey = readlineSync.question('🔐 Ingresa la clave secreta: ', {
    hideEchoBack: true,
    mask: '*'
  });
  
  const uuids = [];
  let continuar = true;
  
  console.log('');
  console.log('📋 Ingresa los UUIDs (presiona ENTER sin texto para terminar):');
  
  while (continuar) {
    const uuid = readlineSync.question(`UUID ${uuids.length + 1}: `);
    
    if (uuid.trim() === '') {
      continuar = false;
    } else {
      uuids.push(uuid.trim());
    }
  }
  
  if (uuids.length === 0) {
    console.log(colores.red + '❌ No se ingresaron UUIDs' + colores.reset);
    readlineSync.question('Presiona ENTER para continuar...');
    return;
  }
  
  console.log('');
  console.log(colores.green + `🔑 CLAVES GENERADAS (${uuids.length} total):` + colores.reset);
  console.log('═'.repeat(60));
  
  uuids.forEach((uuid, index) => {
    try {
      const clave = generarClaveActivacion(uuid, secretKey);
      console.log(`${index + 1:2}. UUID: ${uuid}`);
      console.log(`    Clave: ${colores.bright}${colores.cyan}${clave}${colores.reset}`);
      console.log('');
    } catch (error) {
      console.log(`${index + 1:2}. UUID: ${uuid}`);
      console.log(`    ${colores.red}ERROR: ${error.message}${colores.reset}`);
      console.log('');
    }
  });
  
  console.log('═'.repeat(60));
  readlineSync.question('Presiona ENTER para continuar...');
};

const main = () => {
  let continuar = true;
  
  while (continuar) {
    mostrarTitulo();
    mostrarMenu();
    
    const opcion = readlineSync.question('Ingresa tu opción (1-5): ');
    
    console.log('');
    
    switch (opcion) {
      case '1':
        generarClaveInteractiva();
        break;
      case '2':
        validarClaveInteractiva();
        break;
      case '3':
        mostrarInformacionDetallada();
        break;
      case '4':
        generarMultiplesClaves();
        break;
      case '5':
        console.log(colores.green + '👋 ¡Hasta luego!' + colores.reset);
        continuar = false;
        break;
      default:
        console.log(colores.red + '❌ Opción inválida. Intenta de nuevo.' + colores.reset);
        readlineSync.question('Presiona ENTER para continuar...');
    }
  }
};

// Ejecutar interfaz
main();
