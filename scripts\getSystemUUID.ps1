# Script PowerShell para obtener el UUID del sistema
# Este script se ejecuta de forma oculta para obtener el UUID único de la PC

try {
    # Obtener el UUID del sistema usando WMI
    $computerSystem = Get-WmiObject -Class Win32_ComputerSystemProduct -ErrorAction Stop
    
    if ($computerSystem -and $computerSystem.UUID) {
        # Retornar solo el UUID
        Write-Output $computerSystem.UUID.Trim()
    } else {
        # Si no se puede obtener el UUID, usar el número de serie de la placa base
        $baseBoard = Get-WmiObject -Class Win32_BaseBoard -ErrorAction Stop
        if ($baseBoard -and $baseBoard.SerialNumber) {
            Write-Output $baseBoard.SerialNumber.Trim()
        } else {
            # Como último recurso, usar el ID del procesador
            $processor = Get-WmiObject -Class Win32_Processor -ErrorAction Stop
            Write-Output $processor.ProcessorId.Trim()
        }
    }
} catch {
    # En caso de error, retornar un identificador basado en el nombre del equipo
    Write-Output ([System.Environment]::MachineName + "-" + [System.Environment]::UserName)
}
