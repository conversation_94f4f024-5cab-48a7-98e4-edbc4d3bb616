# Script PowerShell para obtener el UUID del sistema
# Este script se ejecuta de forma oculta para obtener el UUID único de la PC
# Requiere ejecutarse como administrador para acceso completo al hardware

try {
    # Método principal: Usar Get-CimInstance (recomendado para Windows 10+)
    try {
        $computerSystem = Get-CimInstance -ClassName Win32_ComputerSystemProduct -ErrorAction Stop
        if ($computerSystem -and $computerSystem.UUID -and $computerSystem.UUID -ne "00000000-0000-0000-0000-000000000000") {
            Write-Output $computerSystem.UUID.Trim()
            exit 0
        }
    } catch {
        # Continuar con método alternativo
    }

    # Método alternativo: Usar Get-WmiObject (para compatibilidad con versiones anteriores)
    try {
        $computerSystem = Get-WmiObject -Class Win32_ComputerSystemProduct -ErrorAction Stop
        if ($computerSystem -and $computerSystem.UUID -and $computerSystem.UUID -ne "00000000-0000-0000-0000-000000000000") {
            Write-Output $computerSystem.UUID.Trim()
            exit 0
        }
    } catch {
        # Continuar con método alternativo
    }

    # Fallback 1: Usar número de serie de la placa base
    try {
        $baseBoard = Get-CimInstance -ClassName Win32_BaseBoard -ErrorAction Stop
        if ($baseBoard -and $baseBoard.SerialNumber -and $baseBoard.SerialNumber.Trim() -ne "") {
            # Crear UUID basado en el número de serie
            $serial = $baseBoard.SerialNumber.Trim()
            # Generar UUID determinístico basado en el serial
            $hash = [System.Security.Cryptography.MD5]::Create().ComputeHash([System.Text.Encoding]::UTF8.GetBytes($serial))
            $uuid = [System.Guid]::new($hash).ToString().ToUpper()
            Write-Output $uuid
            exit 0
        }
    } catch {
        # Continuar con siguiente método
    }

    # Fallback 2: Usar ID del procesador
    try {
        $processor = Get-CimInstance -ClassName Win32_Processor -ErrorAction Stop | Select-Object -First 1
        if ($processor -and $processor.ProcessorId) {
            # Crear UUID basado en ProcessorId
            $procId = $processor.ProcessorId.Trim()
            $hash = [System.Security.Cryptography.MD5]::Create().ComputeHash([System.Text.Encoding]::UTF8.GetBytes($procId))
            $uuid = [System.Guid]::new($hash).ToString().ToUpper()
            Write-Output $uuid
            exit 0
        }
    } catch {
        # Continuar con último recurso
    }

    # Último recurso: Crear UUID basado en información del sistema
    $machineInfo = [System.Environment]::MachineName + "-" + [System.Environment]::UserName + "-" + [System.Environment]::OSVersion.ToString()
    $hash = [System.Security.Cryptography.MD5]::Create().ComputeHash([System.Text.Encoding]::UTF8.GetBytes($machineInfo))
    $uuid = [System.Guid]::new($hash).ToString().ToUpper()
    Write-Output $uuid

} catch {
    # Error crítico: generar UUID basado en nombre de máquina
    $fallback = [System.Environment]::MachineName + "-FALLBACK-" + (Get-Date -Format "yyyyMMdd")
    $hash = [System.Security.Cryptography.MD5]::Create().ComputeHash([System.Text.Encoding]::UTF8.GetBytes($fallback))
    $uuid = [System.Guid]::new($hash).ToString().ToUpper()
    Write-Output $uuid
}
