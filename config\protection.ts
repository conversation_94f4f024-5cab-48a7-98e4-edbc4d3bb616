// Configuración del sistema de protección

export interface ProtectionConfig {
  // Configuración de activación
  activationRequired: boolean;
  
  // Configuración de desarrollo
  developmentMode: boolean;
  allowTestUUID: boolean;
  
  // Configuración de seguridad
  maxActivationAttempts: number;
  activationCooldownMs: number;
  
  // Configuración de almacenamiento
  storageKeys: {
    activated: string;
    uuid: string;
    attempts: string;
    lastAttempt: string;
  };
  
  // Configuración de UI
  ui: {
    showCopyButton: boolean;
    showSystemInfo: boolean;
    autoFormatKey: boolean;
  };
}

// Configuración por defecto
export const defaultProtectionConfig: ProtectionConfig = {
  activationRequired: true,
  developmentMode: process.env.NODE_ENV === 'development',
  allowTestUUID: process.env.NODE_ENV === 'development',
  maxActivationAttempts: 5,
  activationCooldownMs: 5 * 60 * 1000, // 5 minutos
  
  storageKeys: {
    activated: 'app_activated',
    uuid: 'activation_uuid',
    attempts: 'activation_attempts',
    lastAttempt: 'last_activation_attempt'
  },
  
  ui: {
    showCopyButton: true,
    showSystemInfo: false,
    autoFormatKey: true
  }
};

// Función para obtener configuración con overrides
export const getProtectionConfig = (overrides?: Partial<ProtectionConfig>): ProtectionConfig => {
  return {
    ...defaultProtectionConfig,
    ...overrides
  };
};

// Constantes de seguridad
export const SECURITY_CONSTANTS = {
  KEY_FORMAT_REGEX: /^[A-F0-9]{4}-[A-F0-9]{4}-[A-F0-9]{4}-[A-F0-9]{4}$/,
  UUID_FORMAT_REGEX: /^[0-9A-F]{8}-[0-9A-F]{4}-[0-9A-F]{4}-[0-9A-F]{4}-[0-9A-F]{12}$/i,
  MIN_KEY_LENGTH: 19, // XXXX-XXXX-XXXX-XXXX
  MAX_KEY_LENGTH: 19,
  HMAC_LENGTH: 64, // SHA256 hex length
  KEY_PART_LENGTH: 16 // Primeros 16 caracteres del HMAC
} as const;

// Mensajes de error
export const ERROR_MESSAGES = {
  INVALID_UUID: 'UUID del sistema inválido',
  INVALID_KEY_FORMAT: 'Formato de clave de activación inválido',
  INVALID_KEY: 'Clave de activación incorrecta',
  MAX_ATTEMPTS_REACHED: 'Máximo número de intentos alcanzado. Intenta más tarde.',
  SYSTEM_ERROR: 'Error del sistema. Contacta al soporte técnico.',
  NETWORK_ERROR: 'Error de conexión. Verifica tu conexión a internet.',
  EMPTY_FIELDS: 'Por favor, completa todos los campos',
  UUID_FETCH_ERROR: 'No se pudo obtener el UUID del sistema'
} as const;

// Mensajes de éxito
export const SUCCESS_MESSAGES = {
  ACTIVATION_SUCCESS: 'Activación exitosa. Bienvenido a Battle Strategy Creator.',
  UUID_COPIED: 'UUID copiado al portapapeles',
  KEY_VALIDATED: 'Clave de activación válida'
} as const;
