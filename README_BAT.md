# Archivos .BAT para Battle Strategy Creator

Este proyecto incluye varios archivos .bat para facilitar el uso y administración de la aplicación.

## 📁 Archivos .BAT Disponibles

### 🚀 `inicio.bat` - **ARCHIVO PRINCIPAL**
**Men<PERSON> principal con todas las opciones disponibles.**

```
Opciones disponibles:
1. Ejecutar Aplicación
2. Generar Claves de Activación  
3. Instalar/Configurar Proyecto
4. Verificar Sistema
5. Abrir Documentación
6. Salir
```

**Uso:** Doble clic en `inicio.bat` o ejecutar desde cmd.

---

### ⚙️ `instalar.bat` - Instalación Inicial
**Instala dependencias y configura el proyecto por primera vez.**

**Funciones:**
- Verifica Node.js y npm
- Instala dependencias con `npm install`
- Ejecuta configuración inicial
- Crea archivos de configuración

**Uso:** Se ejecuta automáticamente desde `inicio.bat` o manualmente.

---

### 🎮 `ejecutar.bat` - Ejecutar Aplicación
**Inicia la aplicación en modo desarrollo.**

**Funciones:**
- Verifica dependencias
- Inicia servidor de desarrollo
- Abre la aplicación en http://localhost:5173
- Manejo de errores automático

**Uso:** Para ejecutar la aplicación después de la instalación.

---

### 🔑 `generar-clave.bat` - Generador de Claves
**Herramienta completa para generar claves de activación.**

**Opciones:**
1. **Generar clave individual** - Para un UUID específico
2. **Generar claves masivas** - Desde archivo de texto
3. **Probar sistema** - Ejecutar pruebas de validación
4. **Salir**

**Uso:** Para administradores que necesitan generar claves de activación.

---

### 📦 `compilar.bat` - Compilar para Producción
**Compila la aplicación para despliegue en producción.**

**Funciones:**
- Limpia compilaciones anteriores
- Ejecuta `npm run build`
- Genera archivos optimizados en carpeta `dist/`
- Muestra instrucciones de despliegue

**Uso:** Cuando necesites crear una versión para producción.

---

## 🚀 Guía de Uso Rápido

### Para Usuarios Finales:
1. **Ejecutar `inicio.bat`**
2. **Seleccionar opción 1** (Ejecutar Aplicación)
3. **Seguir instrucciones de activación**

### Para Desarrolladores:
1. **Ejecutar `inicio.bat`**
2. **Seleccionar opción 3** (Instalar/Configurar) - Solo la primera vez
3. **Usar opción 2** (Generar Claves) para crear claves de activación
4. **Usar opción 4** (Verificar Sistema) para diagnósticos

### Para Despliegue:
1. **Ejecutar `compilar.bat`**
2. **Subir contenido de carpeta `dist/` al servidor**
3. **Configurar servidor web**

---

## 🔧 Requisitos del Sistema

### Requisitos Mínimos:
- **Windows 7/8/10/11**
- **Node.js 18+** (se descarga desde https://nodejs.org/)
- **npm** (incluido con Node.js)
- **Conexión a internet** (para instalación de dependencias)

### Verificación de Requisitos:
Ejecuta `inicio.bat` → Opción 4 (Verificar Sistema)

---

## 🛠️ Solución de Problemas

### Error: "Node.js no está instalado"
**Solución:**
1. Descargar Node.js desde https://nodejs.org/
2. Instalar con configuración por defecto
3. Reiniciar cmd/terminal
4. Ejecutar `inicio.bat` nuevamente

### Error: "Las dependencias no están instaladas"
**Solución:**
1. Ejecutar `inicio.bat`
2. Seleccionar opción 3 (Instalar/Configurar)
3. Esperar a que termine la instalación

### Error: "No se pueden instalar las dependencias"
**Solución:**
1. Verificar conexión a internet
2. Ejecutar cmd como administrador
3. Navegar a la carpeta del proyecto
4. Ejecutar `npm install` manualmente

### Error: "Puerto 5173 en uso"
**Solución:**
1. Cerrar otras instancias de la aplicación
2. Reiniciar el sistema si es necesario
3. Ejecutar `inicio.bat` nuevamente

---

## 📋 Comandos Equivalentes

Los archivos .bat ejecutan estos comandos npm:

```bash
# Equivalente a ejecutar.bat
npm run dev

# Equivalente a generar-clave.bat (opción 1)
npm run generate-key "UUID-AQUI"

# Equivalente a generar-clave.bat (opción 3)
npm run test-activation

# Equivalente a compilar.bat
npm run build
```

---

## 🔐 Seguridad

### Para Administradores:
- **NUNCA** distribuir `generar-clave.bat` a usuarios finales
- **MANTENER** el generador de claves en servidor seguro
- **CAMBIAR** la clave secreta antes de producción

### Para Usuarios:
- **SOLO** usar `inicio.bat` y `ejecutar.bat`
- **NO** modificar archivos del sistema
- **CONTACTAR** al administrador para obtener claves de activación

---

## 📞 Soporte

### Problemas Comunes:
1. **Ejecutar `inicio.bat` → Opción 4** para diagnóstico automático
2. **Revisar** PROTECTION_README.md para documentación completa
3. **Contactar** al desarrollador con el resultado del diagnóstico

### Archivos de Log:
Los errores se muestran en pantalla. Para guardar logs:
```cmd
inicio.bat > log.txt 2>&1
```

---

## 📝 Notas Adicionales

- **Todos los archivos .bat** incluyen verificación de errores
- **Colores** en terminal para mejor experiencia de usuario
- **Menús interactivos** para facilidad de uso
- **Documentación integrada** accesible desde los menús
- **Compatibilidad** con Windows 7 en adelante
