{"name": "battle-strategy-creator", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "generate-key": "node scripts/generateActivationKey.js", "test-activation": "node scripts/testActivation.js", "batch-keys": "node scripts/batchGenerateKeys.js", "setup": "node scripts/setup.js", "reset-activation": "node scripts/resetActivation.js", "manage-uuid": "node scripts/manageUUID.js"}, "dependencies": {"crypto-js": "^4.2.0", "react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@types/crypto-js": "^4.2.2", "@types/node": "^22.14.0", "typescript": "~5.7.2", "vite": "^6.2.0"}}