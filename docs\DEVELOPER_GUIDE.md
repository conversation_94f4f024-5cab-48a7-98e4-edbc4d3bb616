# Guía del Desarrollador - Sistema de Protección

## 🔧 Configuración del Entorno de Desarrollo

### Requisitos Previos
- Node.js 18+ 
- npm o yarn
- PowerShell (para Windows)
- Git

### Instalación
```bash
git clone <repository>
cd battle-strategy
npm install
```

### Variables de Entorno
Crear un archivo `.env` en la raíz del proyecto:
```env
NODE_ENV=development
VITE_PROTECTION_MODE=development
VITE_ALLOW_TEST_UUID=true
```

## 🔐 Arquitectura del Sistema de Protección

### Componentes Principales

1. **utils/crypto.ts** - Funciones de cifrado HMAC
2. **components/ActivationScreen.tsx** - UI de activación
3. **hooks/useActivation.ts** - Lógica de estado de activación
4. **config/protection.ts** - Configuración del sistema
5. **scripts/** - Herramientas de generación de claves

### Flujo de Activación

```mermaid
graph TD
    A[Inicio de App] --> B[Verificar Activación]
    B --> C{¿Activado?}
    C -->|Sí| D[Mostrar App Principal]
    C -->|No| E[Obtener UUID Sistema]
    E --> F[Mostrar Pantalla Activación]
    F --> G[Usuario Ingresa Clave]
    G --> H[Validar HMAC]
    H --> I{¿Válida?}
    I -->|Sí| J[Guardar Estado]
    I -->|No| K[Mostrar Error]
    J --> D
    K --> F
```

## 🛠️ Herramientas de Desarrollo

### Generar Clave Individual
```bash
node scripts/generateActivationKey.js "UUID-AQUI"
```

### Generar Claves Masivas
```bash
# Crear archivo con UUIDs (uno por línea)
echo "12345678-1234-1234-1234-123456789012" > uuids.txt
echo "*************-4321-4321-************" >> uuids.txt

# Generar claves
node scripts/batchGenerateKeys.js uuids.txt
```

### Ejecutar Pruebas
```bash
node scripts/testActivation.js
```

## 🔍 Debugging

### Modo Desarrollo
En modo desarrollo, la aplicación:
- Usa UUID de prueba generado aleatoriamente
- Permite bypass de activación (configurable)
- Muestra información adicional de debug

### Logs de Activación
```javascript
// Verificar estado en DevTools Console
localStorage.getItem('app_activated')
localStorage.getItem('activation_uuid')
localStorage.getItem('activation_attempts')
```

### Limpiar Estado de Activación
```javascript
// En DevTools Console
localStorage.removeItem('app_activated');
localStorage.removeItem('activation_uuid');
localStorage.removeItem('activation_attempts');
localStorage.removeItem('last_activation_attempt');
location.reload();
```

## 🚀 Despliegue

### Aplicación Web
```bash
npm run build
# Subir carpeta dist/ al servidor web
```

### Aplicación Electron
```bash
# Instalar Electron
npm install --save-dev electron electron-builder

# Agregar scripts a package.json
{
  "scripts": {
    "electron": "electron electron/main.js",
    "electron-pack": "electron-builder",
    "electron-dev": "NODE_ENV=development electron electron/main.js"
  }
}

# Ejecutar
npm run electron-dev
```

## 🔒 Configuración de Seguridad

### Configuración de Producción
```typescript
const productionConfig = getProtectionConfig({
  activationRequired: true,
  developmentMode: false,
  allowTestUUID: false,
  maxActivationAttempts: 3,
  activationCooldownMs: 10 * 60 * 1000, // 10 minutos
});
```

### Hardening Adicional
1. **Ofuscar código JavaScript** usando herramientas como webpack-obfuscator
2. **Implementar verificación online** para validación adicional
3. **Usar certificados de código** para aplicaciones Electron
4. **Implementar telemetría** para detectar uso no autorizado

## 📝 Personalización

### Cambiar Clave Secreta
1. Modificar `getSecretKey()` en `utils/crypto.ts`
2. Actualizar script generador en `scripts/generateActivationKey.js`
3. Regenerar todas las claves existentes

### Personalizar UI
Modificar `components/ActivationScreen.tsx`:
- Colores y estilos
- Mensajes de texto
- Logo y branding
- Campos adicionales

### Configurar Límites
Editar `config/protection.ts`:
- Número máximo de intentos
- Tiempo de cooldown
- Formato de clave
- Mensajes de error

## 🧪 Testing

### Test Manual
1. Ejecutar aplicación
2. Copiar UUID mostrado
3. Generar clave con script
4. Probar activación
5. Verificar persistencia

### Test Automatizado
```bash
# Ejecutar suite de pruebas
node scripts/testActivation.js

# Verificar generación masiva
node scripts/batchGenerateKeys.js test_uuids.txt
```

## 📊 Monitoreo

### Métricas Importantes
- Intentos de activación fallidos
- UUIDs únicos activados
- Tiempo promedio de activación
- Errores del sistema

### Logs Recomendados
```javascript
// Implementar en producción
console.log('Activation attempt:', {
  uuid: uuid.substring(0, 8) + '...',
  timestamp: new Date().toISOString(),
  success: isValid,
  attempts: attempts
});
```

## 🚨 Troubleshooting Común

### "UUID no puede estar vacío"
- Verificar que PowerShell funcione correctamente
- Implementar fallback para obtener UUID

### "Clave de activación inválida"
- Verificar que la clave secreta coincida
- Comprobar formato de UUID (con/sin guiones)
- Verificar que no haya caracteres especiales

### "Error al obtener UUID del sistema"
- Verificar permisos de PowerShell
- Implementar método alternativo para obtener UUID
- Usar UUID de prueba en desarrollo

## 📚 Referencias

- [CryptoJS Documentation](https://cryptojs.gitbook.io/docs/)
- [Electron Security](https://www.electronjs.org/docs/tutorial/security)
- [HMAC RFC 2104](https://tools.ietf.org/html/rfc2104)
