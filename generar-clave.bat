@echo off
title Battle Strategy Creator - Generador de Claves
color 0B

echo.
echo ========================================
echo   GENERADOR DE CLAVES DE ACTIVACION
echo ========================================
echo.

REM Verificar si Node.js esta instalado
node --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Node.js no esta instalado.
    echo Por favor instala Node.js desde https://nodejs.org/
    echo.
    pause
    exit /b 1
)

REM Verificar si las dependencias estan instaladas
if not exist "node_modules" (
    echo Instalando dependencias...
    echo.
    npm install
    if errorlevel 1 (
        echo ERROR: No se pudieron instalar las dependencias.
        echo.
        pause
        exit /b 1
    )
)

:MENU
echo.
echo Selecciona una opcion:
echo.
echo 1. Generar clave para un UUID especifico
echo 2. Generar claves masivas desde archivo
echo 3. Probar sistema de activacion
echo 4. Salir
echo.
set /p opcion="Ingresa tu opcion (1-4): "

if "%opcion%"=="1" goto GENERAR_INDIVIDUAL
if "%opcion%"=="2" goto GENERAR_MASIVO
if "%opcion%"=="3" goto PROBAR_SISTEMA
if "%opcion%"=="4" goto SALIR

echo Opcion invalida. Intenta de nuevo.
goto MENU

:GENERAR_INDIVIDUAL
echo.
echo ----------------------------------------
echo  GENERAR CLAVE INDIVIDUAL
echo ----------------------------------------
echo.
set /p uuid="Ingresa el UUID: "

if "%uuid%"=="" (
    echo ERROR: Debes ingresar un UUID.
    goto MENU
)

echo.
echo Generando clave para UUID: %uuid%
echo.
node scripts/generateActivationKey.js "%uuid%"

echo.
echo ----------------------------------------
pause
goto MENU

:GENERAR_MASIVO
echo.
echo ----------------------------------------
echo  GENERAR CLAVES MASIVAS
echo ----------------------------------------
echo.
echo Archivos disponibles:
dir /b *.txt 2>nul

echo.
set /p archivo="Ingresa el nombre del archivo con UUIDs: "

if "%archivo%"=="" (
    echo ERROR: Debes ingresar un nombre de archivo.
    goto MENU
)

if not exist "%archivo%" (
    echo ERROR: El archivo %archivo% no existe.
    echo.
    echo Creando archivo de ejemplo...
    echo 12345678-1234-1234-1234-123456789012> example_uuids.txt
    echo *************-4321-4321-************>> example_uuids.txt
    echo ABCDEFGH-ABCD-ABCD-ABCD-ABCDEFGHIJKL>> example_uuids.txt
    echo.
    echo Archivo example_uuids.txt creado con UUIDs de ejemplo.
    goto MENU
)

echo.
echo Generando claves desde archivo: %archivo%
echo.
node scripts/batchGenerateKeys.js "%archivo%"

echo.
echo ----------------------------------------
pause
goto MENU

:PROBAR_SISTEMA
echo.
echo ----------------------------------------
echo  PROBAR SISTEMA DE ACTIVACION
echo ----------------------------------------
echo.
echo Ejecutando pruebas del sistema...
echo.
node scripts/testActivation.js

echo.
echo ----------------------------------------
pause
goto MENU

:SALIR
echo.
echo Saliendo del generador de claves...
echo.
exit /b 0
