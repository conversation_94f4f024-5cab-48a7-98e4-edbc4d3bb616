// Script de configuración inicial del proyecto
import fs from 'fs';
import path from 'path';
import CryptoJS from 'crypto-js';

// Duplicar función aquí para evitar problemas de importación
const getSecretKey = () => {
  const parts = ['Lucas2D', 'r', 'pr', 'dador2025'];
  const vowel = String.fromCharCode(101); // 'e'
  return parts[0] + vowel + parts[1] + vowel + parts[2] + vowel + parts[3];
};

const generateHMAC = (message) => {
  const secretKey = getSecretKey();
  const hash = CryptoJS.HmacSHA256(message, secretKey);
  return hash.toString(CryptoJS.enc.Hex);
};

const generateActivationKey = (uuid) => {
  if (!uuid || uuid.trim() === '') {
    throw new Error('UUID no puede estar vacío');
  }

  const normalizedUUID = uuid.replace(/-/g, '').toUpperCase();
  const hmac = generateHMAC(normalizedUUID);
  const keyPart = hmac.substring(0, 16).toUpperCase();

  return `${keyPart.substring(0, 4)}-${keyPart.substring(4, 8)}-${keyPart.substring(8, 12)}-${keyPart.substring(12, 16)}`;
};

const createDirectories = () => {
  const dirs = [
    'docs',
    'config',
    'utils',
    'hooks',
    'scripts',
    'electron',
    'assets'
  ];
  
  dirs.forEach(dir => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
      console.log(`✅ Directorio creado: ${dir}`);
    } else {
      console.log(`📁 Directorio existe: ${dir}`);
    }
  });
};

const createEnvFile = () => {
  const envContent = `# Configuración de desarrollo
NODE_ENV=development
VITE_PROTECTION_MODE=development
VITE_ALLOW_TEST_UUID=true

# Configuración de producción (comentar en desarrollo)
# NODE_ENV=production
# VITE_PROTECTION_MODE=production
# VITE_ALLOW_TEST_UUID=false
`;

  if (!fs.existsSync('.env')) {
    fs.writeFileSync('.env', envContent);
    console.log('✅ Archivo .env creado');
  } else {
    console.log('📄 Archivo .env ya existe');
  }
};

const createGitignore = () => {
  const gitignoreContent = `# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Production build
dist/
build/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Logs
logs/
*.log

# Activation keys (IMPORTANTE: No subir claves)
activation_keys.txt
*_uuids.txt
keys_*.txt

# Electron
electron-builder.yml
dist_electron/

# Temporary files
tmp/
temp/
`;

  if (!fs.existsSync('.gitignore')) {
    fs.writeFileSync('.gitignore', gitignoreContent);
    console.log('✅ Archivo .gitignore creado');
  } else {
    console.log('📄 Archivo .gitignore ya existe');
  }
};

const createExampleUUIDs = () => {
  const exampleUUIDs = [
    '12345678-1234-1234-1234-123456789012',
    '*************-4321-4321-************',
    'ABCDEFGH-ABCD-ABCD-ABCD-ABCDEFGHIJKL',
    '11111111-**************-************',
    'AAAAAAAA-BBBB-CCCC-DDDD-EEEEEEEEEEEE'
  ];

  const filename = 'example_uuids.txt';
  const content = exampleUUIDs.join('\n') + '\n';
  
  fs.writeFileSync(filename, content);
  console.log(`✅ Archivo de ejemplo creado: ${filename}`);
  
  return exampleUUIDs;
};

const generateExampleKeys = (uuids) => {
  console.log('\n🔑 CLAVES DE EJEMPLO GENERADAS:');
  console.log('='.repeat(50));
  
  uuids.forEach((uuid, index) => {
    try {
      const key = generateActivationKey(uuid);
      console.log(`${index + 1}. UUID: ${uuid}`);
      console.log(`   Clave: ${key}\n`);
    } catch (error) {
      console.log(`${index + 1}. UUID: ${uuid}`);
      console.log(`   Error: ${error.message}\n`);
    }
  });
};

const createPackageScripts = () => {
  const packagePath = 'package.json';
  
  if (fs.existsSync(packagePath)) {
    const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
    
    // Agregar scripts útiles si no existen
    const newScripts = {
      'generate-key': 'node scripts/generateActivationKey.js',
      'test-activation': 'node scripts/testActivation.js',
      'batch-keys': 'node scripts/batchGenerateKeys.js',
      'setup': 'node scripts/setup.js'
    };
    
    let scriptsAdded = false;
    Object.entries(newScripts).forEach(([key, value]) => {
      if (!packageJson.scripts[key]) {
        packageJson.scripts[key] = value;
        scriptsAdded = true;
        console.log(`✅ Script agregado: ${key}`);
      }
    });
    
    if (scriptsAdded) {
      fs.writeFileSync(packagePath, JSON.stringify(packageJson, null, 2));
      console.log('📦 package.json actualizado');
    } else {
      console.log('📦 Scripts ya existen en package.json');
    }
  }
};

const showInstructions = () => {
  console.log('\n' + '='.repeat(60));
  console.log('🎉 CONFIGURACIÓN COMPLETADA');
  console.log('='.repeat(60));
  console.log('\n📋 PRÓXIMOS PASOS:');
  console.log('\n1. Instalar dependencias:');
  console.log('   npm install');
  console.log('\n2. Ejecutar en modo desarrollo:');
  console.log('   npm run dev');
  console.log('\n3. Generar clave de activación:');
  console.log('   npm run generate-key "TU-UUID-AQUI"');
  console.log('\n4. Probar sistema de activación:');
  console.log('   npm run test-activation');
  console.log('\n5. Generar claves masivas:');
  console.log('   npm run batch-keys example_uuids.txt');
  console.log('\n📚 DOCUMENTACIÓN:');
  console.log('   - README: PROTECTION_README.md');
  console.log('   - Guía desarrollador: docs/DEVELOPER_GUIDE.md');
  console.log('\n🔐 ARCHIVOS IMPORTANTES:');
  console.log('   - Configuración: config/protection.ts');
  console.log('   - Cifrado: utils/crypto.ts');
  console.log('   - UI Activación: components/ActivationScreen.tsx');
  console.log('\n⚠️  RECORDATORIO DE SEGURIDAD:');
  console.log('   - NO subir claves de activación al repositorio');
  console.log('   - Mantener scripts generadores en servidor seguro');
  console.log('   - Cambiar clave secreta antes de producción');
  console.log('\n' + '='.repeat(60));
};

const main = () => {
  console.log('🚀 CONFIGURANDO PROYECTO BATTLE STRATEGY CREATOR');
  console.log('='.repeat(60));
  
  try {
    console.log('\n📁 Creando directorios...');
    createDirectories();
    
    console.log('\n⚙️  Creando archivos de configuración...');
    createEnvFile();
    createGitignore();
    
    console.log('\n📝 Creando archivos de ejemplo...');
    const exampleUUIDs = createExampleUUIDs();
    
    console.log('\n📦 Actualizando package.json...');
    createPackageScripts();
    
    console.log('\n🔑 Generando claves de ejemplo...');
    generateExampleKeys(exampleUUIDs);
    
    showInstructions();
    
  } catch (error) {
    console.error('\n❌ Error durante la configuración:', error.message);
    process.exit(1);
  }
};

// Ejecutar configuración
main();
