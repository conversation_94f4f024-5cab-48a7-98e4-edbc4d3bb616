# 🔑 Generador de Claves de Activación

Sistema independiente para generar claves de activación de 16 dígitos para **Battle Strategy Creator**.

## 🚀 Inicio Rápido

### Windows (.BAT)
```bash
# Doble clic en ejecutar.bat
ejecutar.bat
```

### Línea de Comandos
```bash
# Instalar dependencias
npm install

# Modo interactivo
npm start

# Interfaz completa
npm run interfaz

# Generar clave directamente
npm run generar "UUID-AQUI" "CLAVE-SECRETA"

# Ejecutar pruebas
npm run test
```

## 🔐 Cómo Funciona

### Proceso de Activación:
1. **Usuario ejecuta Battle Strategy Creator**
2. **Aplicación muestra UUID del sistema**
3. **Usuario te envía el UUID**
4. **Tú generas la clave con este sistema**
5. **Proporcionas la clave al usuario**
6. **Usuario activa su aplicación**

### Algoritmo:
- **Entrada**: UUID del sistema + Clave secreta
- **Proceso**: HMAC SHA256
- **Salida**: Primeros 16 dígitos del HMAC
- **Formato**: `XXXX-XXXX-XXXX-XXXX`

## 📋 Ejemplos

### Ejemplo 1:
```
UUID: 12345678-1234-1234-1234-123456789012
Clave Secreta: Lucas2Derepredador2025
Resultado: 9511-10EB-3B6E-F379
```

### Ejemplo 2:
```
UUID: *************-4321-4321-************
Clave Secreta: Lucas2Derepredador2025
Resultado: E825-1A0B-FE9C-FF93
```

## 🎛️ Modos de Uso

### 1. Modo Rápido
Interfaz simple para generar una clave rápidamente.

```bash
npm start
# Seleccionar opción 1
```

### 2. Interfaz Interactiva
Interfaz completa con múltiples opciones y validación.

```bash
npm run interfaz
```

**Características:**
- 🔑 Generar clave individual
- ✅ Validar clave existente
- 📊 Información detallada del proceso
- 📋 Generar múltiples claves
- 🎨 Interfaz con colores

### 3. Línea de Comandos
Para automatización y scripts.

```bash
npm run generar "UUID" "CLAVE_SECRETA"
```

### 4. Pruebas
Verificar que el sistema funciona correctamente.

```bash
npm run test
```

## 📁 Estructura de Archivos

```
generadorkey/
├── package.json          # Configuración del proyecto
├── index.js             # Punto de entrada principal
├── generador.js         # Lógica de generación HMAC
├── interfaz.js          # Interfaz interactiva
├── test.js              # Suite de pruebas
├── ejecutar.bat         # Archivo .bat para Windows
└── README.md            # Esta documentación
```

## 🔧 Funciones Principales

### `generarClaveActivacion(uuid, secretKey)`
Genera una clave de activación de 16 dígitos.

```javascript
import { generarClaveActivacion } from './generador.js';

const clave = generarClaveActivacion(
  '12345678-1234-1234-1234-123456789012',
  'Lucas2Derepredador2025'
);
console.log(clave); // 9511-10EB-3B6E-F379
```

### `validarClaveActivacion(uuid, secretKey, clave)`
Valida si una clave es correcta para un UUID.

```javascript
import { validarClaveActivacion } from './generador.js';

const esValida = validarClaveActivacion(
  '12345678-1234-1234-1234-123456789012',
  'Lucas2Derepredador2025',
  '9511-10EB-3B6E-F379'
);
console.log(esValida); // true
```

### `generarInformacionDetallada(uuid, secretKey)`
Obtiene información completa del proceso de generación.

```javascript
import { generarInformacionDetallada } from './generador.js';

const info = generarInformacionDetallada(
  '12345678-1234-1234-1234-123456789012',
  'Lucas2Derepredador2025'
);

console.log(info);
/*
{
  uuidOriginal: '12345678-1234-1234-1234-123456789012',
  uuidNormalizado: '12345678123412341234123456789012',
  claveSecreta: 'Lucas2Derepredador2025',
  hmacCompleto: '951110eb3b6ef379025638c5aa369f537b05aa0c6f7fe61d9741c27eb35168aa',
  primeros16Digitos: '951110EB3B6EF379',
  claveActivacion: '9511-10EB-3B6E-F379',
  longitudHmac: 64,
  longitudClave: 19,
  timestamp: '2025-01-14T...'
}
*/
```

## 🛡️ Seguridad

### ✅ Implementado:
- **HMAC SHA256** para generación robusta
- **Validación de entrada** para UUIDs y claves
- **Normalización** de formatos de UUID
- **Manejo de errores** completo

### ⚠️ Recomendaciones:
- **Mantén este generador seguro** - No lo distribuyas a usuarios finales
- **Protege la clave secreta** - Cámbiala antes de producción
- **Usa conexión segura** - Para enviar claves a usuarios
- **Registra activaciones** - Para control y auditoría

## 🧪 Pruebas

El sistema incluye una suite completa de pruebas:

```bash
npm run test
```

**Pruebas incluidas:**
- ✅ Generación básica
- ✅ Consistencia de resultados
- ✅ UUIDs diferentes generan claves diferentes
- ✅ Validación de claves
- ✅ Formatos de UUID (con/sin guiones)
- ✅ Manejo de errores
- ✅ Información detallada
- ✅ Casos conocidos

## 📊 Formatos Soportados

### UUID de Entrada:
```
✅ 12345678-1234-1234-1234-123456789012  (con guiones)
✅ 12345678123412341234123456789012      (sin guiones)
✅ 12345678 1234 1234 1234 123456789012  (con espacios)
```

### Clave de Salida:
```
✅ 9511-10EB-3B6E-F379  (siempre con guiones)
```

## 🚨 Solución de Problemas

### Error: "UUID debe tener formato válido"
**Causa:** UUID no tiene 32 caracteres hexadecimales
**Solución:** Verificar que el UUID sea correcto

### Error: "Clave secreta es requerida"
**Causa:** Clave secreta vacía o no proporcionada
**Solución:** Proporcionar la clave secreta correcta

### Error: "Node.js no está instalado"
**Causa:** Node.js no está disponible
**Solución:** Instalar Node.js desde https://nodejs.org/

## 📞 Soporte

### Para generar claves:
1. Ejecutar `ejecutar.bat` (Windows) o `npm start`
2. Seguir las instrucciones en pantalla
3. Proporcionar UUID y clave secreta
4. Copiar la clave generada

### Para validar el sistema:
```bash
npm run test
```

### Para obtener ayuda:
```bash
npm start
# Seleccionar opción 3 (Ayuda)
```

---

## 🎯 Resumen

Este generador es un **sistema independiente y seguro** para crear claves de activación de 16 dígitos usando **HMAC SHA256**. 

**Características principales:**
- 🔐 Algoritmo HMAC SHA256 robusto
- 📋 Múltiples interfaces de uso
- 🧪 Suite completa de pruebas
- 🛡️ Validación y manejo de errores
- 🎨 Interfaz amigable con colores
- 📁 Sistema modular y extensible

**¡Listo para proteger tu aplicación Battle Strategy Creator!** 🚀
