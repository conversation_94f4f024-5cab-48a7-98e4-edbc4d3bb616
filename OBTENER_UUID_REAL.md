# 🔍 Cómo Obtener el UUID Real del Sistema

## 🎯 **¿Por qué necesitas el UUID real?**

El **UUID del hardware** es un identificador único de tu placa base que **nunca cambia**. Esto garantiza que:
- ✅ **Una sola activación** por PC
- ✅ **UUID siempre igual** entre sesiones
- ✅ **Identificación única** del hardware
- ✅ **Seguridad robusta** del sistema

---

## 🚀 **Método 1: Script Automático (RECOMENDADO)**

### **Ejecutar script .bat:**
```bash
# 1. Ir a la carpeta del proyecto
cd battle-strategy

# 2. Ejecutar script como administrador
# (Clic derecho → "Ejecutar como administrador")
scripts/obtener-uuid.bat
```

### **Resultado esperado:**
```
========================================
  OBTENER UUID DEL SISTEMA
========================================

✅ Ejecutandose como administrador

📋 Método 1: Get-CimInstance
✅ UUID encontrado: 12345678-1234-5678-9012-ABCDEFGHIJKL

📋 Método 2: Get-WmiObject  
✅ UUID encontrado: 12345678-1234-5678-9012-ABCDEFGHIJKL

✅ UUID guardado en uuid_sistema.txt

💡 Usa este UUID para generar tu clave:
   cd generadorkey
   npm run generar "12345678-1234-5678-9012-ABCDEFGHIJKL" "Lucas2Derepredador2025"
```

---

## 🖥️ **Método 2: PowerShell Manual**

### **Comando principal (Windows 10+):**
```powershell
# Abrir PowerShell como administrador
Get-CimInstance -ClassName Win32_ComputerSystemProduct | Select-Object -ExpandProperty UUID
```

### **Comando alternativo (Windows 7/8):**
```powershell
# Abrir PowerShell como administrador
(Get-WmiObject -Class Win32_ComputerSystemProduct).UUID
```

### **Pasos detallados:**
1. **Presionar** `Win + X`
2. **Seleccionar** "Windows PowerShell (Administrador)"
3. **Ejecutar** uno de los comandos de arriba
4. **Copiar** el UUID mostrado

---

## 🔧 **Método 3: Línea de Comandos (CMD)**

### **Usando WMIC:**
```cmd
# Abrir CMD como administrador
wmic csproduct get uuid
```

### **Resultado esperado:**
```
UUID
12345678-1234-5678-9012-ABCDEFGHIJKL
```

---

## ⚠️ **Problemas Comunes y Soluciones**

### **Problema 1: UUID es 00000000-0000-0000-0000-000000000000**
**Causa:** Tu sistema no tiene UUID de hardware válido
**Soluciones:**
```powershell
# Opción A: Usar número de serie de placa base
Get-CimInstance -ClassName Win32_BaseBoard | Select-Object SerialNumber

# Opción B: Usar ID del procesador
Get-CimInstance -ClassName Win32_Processor | Select-Object ProcessorId
```

### **Problema 2: "Acceso denegado"**
**Causa:** No se está ejecutando como administrador
**Solución:**
1. Cerrar PowerShell/CMD
2. Clic derecho en PowerShell/CMD
3. Seleccionar "Ejecutar como administrador"

### **Problema 3: "Get-CimInstance no reconocido"**
**Causa:** Versión antigua de Windows
**Solución:** Usar comando alternativo:
```powershell
(Get-WmiObject -Class Win32_ComputerSystemProduct).UUID
```

### **Problema 4: UUID cambia entre ejecuciones**
**Causa:** Usando UUID generado en lugar del real
**Solución:** Seguir los métodos de arriba para obtener UUID de hardware

---

## 🛠️ **Verificación del UUID**

### **Características de un UUID real:**
- ✅ **Formato:** 8-4-4-4-12 caracteres hexadecimales
- ✅ **Ejemplo:** `12345678-1234-5678-9012-ABCDEFGHIJKL`
- ✅ **Consistente:** Siempre el mismo en cada ejecución
- ✅ **No nulo:** No es `00000000-0000-0000-0000-000000000000`

### **Comando de verificación:**
```powershell
# Ejecutar varias veces - debería dar el mismo resultado
Get-CimInstance -ClassName Win32_ComputerSystemProduct | Select-Object -ExpandProperty UUID
Get-CimInstance -ClassName Win32_ComputerSystemProduct | Select-Object -ExpandProperty UUID
Get-CimInstance -ClassName Win32_ComputerSystemProduct | Select-Object -ExpandProperty UUID
```

---

## 🔐 **Generar Clave de Activación**

### **Una vez que tengas el UUID real:**

```bash
# 1. Ir a la carpeta del generador
cd generadorkey

# 2. Generar clave con tu UUID real
npm run generar "TU-UUID-REAL" "Lucas2Derepredador2025"

# Ejemplo:
npm run generar "12345678-1234-5678-9012-ABCDEFGHIJKL" "Lucas2Derepredador2025"
```

### **Resultado:**
```
🔑 CLAVE DE ACTIVACIÓN GENERADA:
🔑 A1B2-C3D4-E5F6-7890
```

---

## 📱 **Para Diferentes Sistemas**

### **Windows 10/11:**
```powershell
Get-CimInstance -ClassName Win32_ComputerSystemProduct | Select-Object -ExpandProperty UUID
```

### **Windows 7/8:**
```powershell
(Get-WmiObject -Class Win32_ComputerSystemProduct).UUID
```

### **Máquinas Virtuales:**
- ⚠️ **Pueden no tener UUID válido**
- 🔄 **Usar número de serie de placa base como alternativa**
- 💡 **Configurar UUID en la VM si es posible**

---

## 🚨 **Casos Especiales**

### **Sistemas sin UUID válido:**
Si tu sistema no tiene UUID de hardware, puedes usar:

```powershell
# Opción 1: Serial de placa base
$board = Get-CimInstance -ClassName Win32_BaseBoard
$serial = $board.SerialNumber
Write-Host "Serial de placa base: $serial"

# Opción 2: ID del procesador  
$cpu = Get-CimInstance -ClassName Win32_Processor | Select-Object -First 1
$cpuId = $cpu.ProcessorId
Write-Host "ID del procesador: $cpuId"
```

### **Crear UUID personalizado:**
```powershell
# Generar UUID basado en información del sistema
$info = $env:COMPUTERNAME + "-" + $env:USERNAME + "-" + (Get-Date -Format "yyyyMMdd")
$bytes = [System.Text.Encoding]::UTF8.GetBytes($info)
$hash = [System.Security.Cryptography.MD5]::Create().ComputeHash($bytes)
$uuid = [System.Guid]::new($hash).ToString().ToUpper()
Write-Host "UUID personalizado: $uuid"
```

---

## 📊 **Comparación de Métodos**

| Método | Facilidad | Confiabilidad | Requiere Admin |
|--------|-----------|---------------|----------------|
| Script .bat | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ✅ Sí |
| PowerShell | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ✅ Sí |
| CMD/WMIC | ⭐⭐⭐ | ⭐⭐⭐⭐ | ✅ Sí |
| UUID Generado | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | ❌ No |

---

## 🎯 **Recomendaciones**

### **Para Usuarios Finales:**
1. **Usar script automático** `obtener-uuid.bat`
2. **Ejecutar como administrador** siempre
3. **Guardar el UUID** para futuras referencias
4. **Verificar consistencia** ejecutando varias veces

### **Para Administradores:**
1. **Proporcionar script** a los usuarios
2. **Documentar proceso** claramente
3. **Verificar UUIDs** antes de generar claves
4. **Mantener registro** de UUIDs y claves

### **Para Soporte Técnico:**
1. **Guiar usuarios** paso a paso
2. **Verificar permisos** de administrador
3. **Usar métodos alternativos** si es necesario
4. **Documentar casos especiales**

---

## 🎉 **Resumen**

**Para obtener tu UUID real:**
1. 🚀 **Ejecuta** `scripts/obtener-uuid.bat` como administrador
2. 📋 **Copia** el UUID mostrado
3. 🔑 **Genera** tu clave de activación
4. ✅ **Activa** tu aplicación

**El UUID real garantiza una activación única y permanente para tu PC.** 🛡️
