# 🔗 Sistema de Activación Basado en MAC Address

## 🎯 **¿Por qué MAC Address en lugar de UUID?**

El sistema ahora usa **MAC Address del adaptador de red** porque es:
- ✅ **Más estable** - No cambia como el UUID del sistema
- ✅ **Único por hardware** - Cada tarjeta de red tiene MAC única
- ✅ **Siempre disponible** - Todos los equipos tienen adaptador de red
- ✅ **Fácil de obtener** - No requiere permisos especiales
- ✅ **Consistente** - Mismo valor en cada ejecución

---

## 🚀 **Cómo Funciona el Nuevo Sistema**

### **1. Obtención de MAC Address Real:**
```powershell
# El script PowerShell obtiene la MAC del adaptador principal
Get-NetAdapter | Where-Object { $_.Status -eq "Up" } | Select-Object MacAddress
```

### **2. Generación de Clave:**
```bash
# Usar MAC Address directamente para generar clave
cd generadorkey
npm run generar "AABBCCDDEEFF" "Lucas2Derepredador2025"
```

### **3. Activación:**
- La aplicación lee la MAC real del archivo generado por PowerShell
- Muestra la MAC en la interfaz
- Usuario genera clave con esa MAC
- Sistema valida usando HMAC SHA256

---

## 🛠️ **Archivos del Sistema**

### **Script Principal: `ejecutar.ps1`**
```powershell
# Obtiene MAC Address real del adaptador de red principal
# Guarda en mac_address.json y mac_address.txt
# Inicia la aplicación automáticamente
```

### **Ejecutor: `ejecutar-admin.bat`**
```batch
# Ejecuta el script PowerShell
# Solicita permisos de administrador si es necesario
# Maneja errores automáticamente
```

### **Archivos Generados:**
- **`mac_address.json`** - Información completa de la MAC
- **`mac_address.txt`** - Solo la MAC Address

---

## 🔧 **Uso Paso a Paso**

### **Paso 1: Obtener MAC Address Real**
```bash
# Doble clic en ejecutar-admin.bat
# O ejecutar manualmente:
.\ejecutar.ps1
```

**Resultado esperado:**
```
╔══════════════════════════════════════════════════════════════╗
║                  MAC ADDRESS DEL SISTEMA                    ║
╚══════════════════════════════════════════════════════════════╝

🔑 MAC Address: AABBCCDDEEFF
📡 Adaptador: Ethernet
📋 Descripción: Intel(R) Ethernet Connection
🔧 Método: Active Network Adapter

✅ MAC Address guardada en archivos para la aplicación
```

### **Paso 2: Generar Clave de Activación**
```bash
cd generadorkey
npm run generar "AABBCCDDEEFF" "Lucas2Derepredador2025"
```

**Resultado:**
```
🔑 GENERADOR DE CLAVES DE ACTIVACIÓN
=====================================

📋 Identificador Original: AABBCCDDEEFF
🔧 Identificador Normalizado: AABBCCDDEEFF
🔐 Clave Secreta: Lucas2Derepredador2025
📊 HMAC SHA256: 1234567890abcdef...
🎯 Primeros 16 dígitos: 1234567890ABCDEF

✅ CLAVE DE ACTIVACIÓN GENERADA:
🔑 1234-5678-90AB-CDEF
```

### **Paso 3: Activar Aplicación**
1. Abrir la aplicación (se abre automáticamente)
2. Ver la MAC Address en la interfaz
3. Introducir la clave generada
4. Hacer clic en "Activar"

---

## 🎨 **Interfaz Mejorada**

### **MAC Address Real (Verde):**
```
┌─────────────────────────────────────────────┐
│ MAC Address del Sistema: AABBCCDDEEFF       │
│ [📋 Copiar]                                 │
│                                             │
│ ✅ MAC Address Real del Hardware            │
│ MAC Address obtenida del hardware          │
│ Fuente: Active Network Adapter             │
│                                             │
│ 🎉 Perfecto! Esta es la MAC real           │
│ 📋 Úsala para generar tu clave             │
└─────────────────────────────────────────────┘
```

### **MAC Address Generada (Naranja):**
```
┌─────────────────────────────────────────────┐
│ MAC Address del Sistema: 02XXXXXXXXXXXX     │
│ [📋 Copiar]                                 │
│                                             │
│ ⚠️ MAC Address Generada                     │
│ MAC Address generada de forma consistente  │
│ Fuente: Generated Consistent               │
│                                             │
│ 🚨 IMPORTANTE: Para obtener MAC REAL:      │
│ 1. Ejecuta "ejecutar-admin.bat" como admin │
│ 2. Luego recarga esta página               │
└─────────────────────────────────────────────┘
```

---

## 🔍 **Detección de Adaptadores**

### **Prioridad de Adaptadores:**
1. **Adaptador activo** (Status = "Up")
2. **Adaptador físico** (no virtual)
3. **Adaptador Ethernet** (preferido sobre WiFi)
4. **Primer adaptador válido** encontrado

### **Adaptadores Excluidos:**
- ❌ Adaptadores virtuales (VMware, VirtualBox)
- ❌ Adaptadores Bluetooth
- ❌ Adaptadores de loopback
- ❌ Adaptadores desconectados

### **Métodos de Obtención:**
1. **Get-NetAdapter** (Windows 10+)
2. **Get-WmiObject** (Windows 7+)
3. **Get-CimInstance** (alternativo)
4. **getmac** (comando de emergencia)

---

## 📊 **Formato de MAC Address**

### **Formato de Entrada (Aceptados):**
- `AA:BB:CC:DD:EE:FF` (con dos puntos)
- `AA-BB-CC-DD-EE-FF` (con guiones)
- `AABBCCDDEEFF` (sin separadores)

### **Formato Normalizado:**
- `AABBCCDDEEFF` (12 caracteres hexadecimales)
- Sin separadores
- Mayúsculas

### **Validación:**
```javascript
// Validar formato MAC (12 caracteres hexadecimales)
if (cleanMAC.match(/^[0-9A-F]{12}$/)) {
  return cleanMAC; // MAC válida
}
```

---

## 🔐 **Seguridad del Sistema**

### **Encriptación HMAC SHA256:**
```javascript
// Generar HMAC usando MAC Address
const hmac = CryptoJS.HmacSHA256(macAddress, "Lucas2Derepredador2025");
const activationKey = hmac.substring(0, 16); // Primeros 16 caracteres
```

### **Clave Secreta Ofuscada:**
```javascript
// Clave secreta construida de forma ofuscada
const parts = ['Lucas2D', 'r', 'pr', 'dador2025'];
const vowel = String.fromCharCode(101); // 'e'
const secretKey = parts[0] + vowel + parts[1] + vowel + parts[2] + vowel + parts[3];
// Resultado: "Lucas2Derepredador2025"
```

### **Validación de Activación:**
```javascript
// Verificar que la clave corresponde a la MAC
const expectedKey = generateActivationKey(macAddress);
return expectedKey.toLowerCase() === providedKey.toLowerCase();
```

---

## 🛡️ **Ventajas del Sistema MAC**

### **Vs UUID del Sistema:**
- ✅ **Más estable** - UUID puede cambiar
- ✅ **Más confiable** - MAC siempre disponible
- ✅ **Más fácil** - No requiere permisos especiales
- ✅ **Más compatible** - Funciona en todos los sistemas

### **Vs Otros Identificadores:**
- ✅ **Único** - Cada tarjeta tiene MAC única
- ✅ **Persistente** - No cambia al reinstalar SO
- ✅ **Verificable** - Fácil de comprobar manualmente
- ✅ **Estándar** - Formato IEEE estándar

---

## 🚨 **Casos Especiales**

### **Múltiples Adaptadores:**
- El script selecciona automáticamente el adaptador principal
- Prioriza adaptadores activos y físicos
- Excluye adaptadores virtuales

### **Adaptadores Virtuales:**
- Se detectan y excluyen automáticamente
- Busca adaptadores físicos reales
- Usa métodos alternativos si es necesario

### **Sin Adaptadores Físicos:**
- Genera MAC consistente como fallback
- Basada en características del sistema
- Funcional pero marcada como "generada"

---

## 🎯 **Flujo Completo**

### **Para Nuevos Usuarios:**
```
1. Descargar/clonar el proyecto
2. Doble clic en "ejecutar-admin.bat"
3. Script obtiene MAC real automáticamente
4. Aplicación se abre mostrando MAC real
5. Copiar MAC Address mostrada
6. Generar clave: npm run generar "MAC" "Lucas2Derepredador2025"
7. Introducir clave en la aplicación
8. Activar y usar Battle Strategy Creator
```

### **Para Usuarios Existentes:**
```
1. Ejecutar "ejecutar-admin.bat" para obtener MAC real
2. Recargar aplicación
3. Verificar que muestra MAC real (verde)
4. Generar nueva clave con MAC real
5. Reactivar aplicación
```

---

## 🎉 **Beneficios Implementados**

### **Para Usuarios:**
- 🖱️ **Un clic** para obtener MAC real
- 📊 **Información visual** del estado de la MAC
- 🔄 **Proceso automático** sin comandos manuales
- ✅ **Activación más confiable**

### **Para Administradores:**
- 🔑 **Identificación única** por adaptador de red
- 🛡️ **Seguridad robusta** con HMAC SHA256
- 📋 **Información completa** del adaptador
- 🔧 **Herramientas de diagnóstico**

### **Para el Sistema:**
- 🎯 **Identificación estable** del hardware
- 🔒 **Protección contra duplicación**
- 📊 **Detección automática** de adaptadores
- 🔄 **Fallbacks seguros** en casos especiales

---

## 🚀 **Resumen**

**Battle Strategy Creator ahora:**
- 🔗 **Usa MAC Address** del adaptador de red real
- 🖱️ **Obtención automática** con un clic
- 📊 **Interfaz visual** que muestra el estado
- 🛠️ **Herramientas completas** de gestión
- 📚 **Documentación detallada**
- ✅ **Sistema más estable** y confiable

**¡La activación ahora es más estable y confiable usando MAC Address real!** 🛡️🔗
