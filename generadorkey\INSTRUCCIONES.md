# 🔑 INSTRUCCIONES - Generador de Claves

## 🚀 **INICIO RÁPIDO**

### **Para Windows (Recomendado):**
```
1. Doble clic en "ejecutar.bat"
2. Seleccionar opción 1 (Modo Rápido)
3. Introducir UUID del sistema
4. Introducir clave secreta: Lucas2Derepredador2025
5. <PERSON>piar la clave generada
```

### **Para Línea de Comandos:**
```bash
# Generar clave directamente
npm run generar "UUID-AQUI" "Lucas2Derepredador2025"

# Ejemplo:
npm run generar "************************************" "Lucas2Derepredador2025"
# Resultado: 9511-10EB-3B6E-F379
```

---

## 📋 **PROCESO COMPLETO**

### **1. Usuario solicita activación:**
- Usuario ejecuta Battle Strategy Creator
- Aplicación muestra UUID del sistema
- Usuario te envía el UUID

### **2. Tú generas la clave:**
- Abres este generador
- Introduces el UUID del usuario
- Introduces la clave secreta: `Lucas2Derepredador2025`
- Obtienes la clave de 16 dígitos

### **3. Usuario activa su aplicación:**
- Le proporcionas la clave generada
- Usuario introduce la clave en su aplicación
- Aplicación se activa permanentemente

---

## 🔐 **DATOS IMPORTANTES**

### **Clave Secreta:**
```
Lucas2Derepredador2025
```
**⚠️ MANTENER SECRETA - No compartir con usuarios**

### **Formato de UUID:**
```
✅ ************************************  (con guiones)
✅ 12345678123412341234123456789012      (sin guiones)
```

### **Formato de Clave Generada:**
```
✅ 9511-10EB-3B6E-F379  (16 dígitos hexadecimales)
```

---

## 🛠️ **MÉTODOS DE USO**

### **Método 1: Archivo .BAT (Más Fácil)**
```
ejecutar.bat → Opción 1 → Introducir datos → Copiar clave
```

### **Método 2: Interfaz Interactiva**
```bash
npm run interfaz
```
- Menú completo con colores
- Validación automática
- Múltiples opciones

### **Método 3: Línea de Comandos**
```bash
npm run generar "UUID" "CLAVE_SECRETA"
```
- Para automatización
- Más rápido para uso frecuente

### **Método 4: Menú Principal**
```bash
npm start
```
- Interfaz simple y directa

---

## 📊 **EJEMPLOS REALES**

### **Ejemplo 1:**
```
UUID Usuario: ************************************
Comando: npm run generar "************************************" "Lucas2Derepredador2025"
Resultado: 9511-10EB-3B6E-F379
```

### **Ejemplo 2:**
```
UUID Usuario: ************************************
Comando: npm run generar "************************************" "Lucas2Derepredador2025"
Resultado: E825-1A0B-FE9C-FF93
```

### **Ejemplo 3:**
```
UUID Usuario: ABCDEFGH-ABCD-ABCD-ABCD-ABCDEFGHIJKL
Comando: npm run generar "ABCDEFGH-ABCD-ABCD-ABCD-ABCDEFGHIJKL" "Lucas2Derepredador2025"
Resultado: 1BB5-60F5-CA70-A2C8
```

---

## ✅ **VERIFICACIÓN**

### **Probar que funciona:**
```bash
npm run test
```
**Debe mostrar:** `🎉 TODAS LAS PRUEBAS COMPLETADAS`

### **Verificar clave generada:**
```bash
npm run interfaz
# Seleccionar opción 2 (Validar clave)
# Introducir UUID, clave secreta y clave generada
# Debe mostrar: ✅ CLAVE VÁLIDA
```

---

## 🚨 **SOLUCIÓN DE PROBLEMAS**

### **Error: "Node.js no está instalado"**
**Solución:** Instalar Node.js desde https://nodejs.org/

### **Error: "UUID debe tener formato válido"**
**Solución:** Verificar que el UUID tenga 32 caracteres hexadecimales

### **Error: "Dependencias no instaladas"**
**Solución:** 
```bash
npm install
```

### **La clave no funciona en la aplicación**
**Verificar:**
1. UUID copiado correctamente (sin espacios extra)
2. Clave secreta correcta: `Lucas2Derepredador2025`
3. Clave copiada sin errores

---

## 📞 **FLUJO DE SOPORTE**

### **Usuario te contacta:**
```
Usuario: "Necesito activar Battle Strategy Creator"
Tú: "Envíame el UUID que aparece en la pantalla de activación"
```

### **Usuario envía UUID:**
```
Usuario: "Mi UUID es: ************************************"
Tú: [Generas clave con este sistema]
```

### **Tú respondes:**
```
Tú: "Tu clave de activación es: 9511-10EB-3B6E-F379"
Tú: "Introdúcela en la aplicación y presiona Activar"
```

### **Confirmación:**
```
Usuario: "¡Funcionó! La aplicación está activada"
Tú: "Perfecto. La activación es permanente."
```

---

## 🛡️ **SEGURIDAD**

### **✅ HACER:**
- Mantener este generador privado
- Usar solo la clave secreta correcta
- Verificar UUIDs antes de generar claves
- Guardar registro de claves generadas (opcional)

### **❌ NO HACER:**
- Compartir este generador con usuarios
- Cambiar la clave secreta sin actualizar la aplicación
- Generar claves para UUIDs sospechosos
- Distribuir claves públicamente

---

## 📈 **ESTADÍSTICAS**

Cada vez que generes una clave, el sistema:
- ✅ Valida el formato del UUID
- ✅ Normaliza el UUID (remueve guiones)
- ✅ Genera HMAC SHA256 con la clave secreta
- ✅ Toma los primeros 16 dígitos del HMAC
- ✅ Formatea como XXXX-XXXX-XXXX-XXXX
- ✅ Proporciona información detallada del proceso

**Resultado:** Clave única e irrepetible para cada UUID.

---

## 🎯 **RESUMEN**

**Este generador es tu herramienta principal para:**
- 🔑 Generar claves de activación seguras
- 📋 Procesar solicitudes de usuarios
- 🛡️ Mantener la protección de tu aplicación
- ✅ Validar claves existentes

**¡Listo para proteger Battle Strategy Creator!** 🚀
