# Script PowerShell para ejecutar Battle Strategy Creator con MAC Address real
# Este script debe ejecutarse como administrador para obtener la MAC del hardware

param(
    [switch]$Force,
    [switch]$Debug
)

# Configuración de colores y estilo
$Host.UI.RawUI.WindowTitle = "Battle Strategy Creator - MAC Address Real"
$Host.UI.RawUI.BackgroundColor = "DarkBlue"
$Host.UI.RawUI.ForegroundColor = "White"
Clear-Host

# Función para escribir con colores
function Write-ColorText {
    param(
        [string]$Text,
        [string]$Color = "White"
    )
    Write-Host $Text -ForegroundColor $Color
}

# Función para verificar permisos de administrador
function Test-Administrator {
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

# Función para obtener MAC Address real del adaptador de red principal
function Get-RealMACAddress {
    try {
        Write-ColorText "🔍 Obteniendo MAC Address del adaptador de red..." "Yellow"
        
        # Método 1: Obtener adaptador de red activo (con conexión)
        try {
            $activeAdapter = Get-NetAdapter | Where-Object { 
                $_.Status -eq "Up" -and 
                $_.PhysicalMediaType -ne $null -and 
                $_.InterfaceDescription -notlike "*Virtual*" -and
                $_.InterfaceDescription -notlike "*Loopback*" -and
                $_.InterfaceDescription -notlike "*Bluetooth*" -and
                $_.Name -notlike "*VMware*" -and
                $_.Name -notlike "*VirtualBox*"
            } | Sort-Object InterfaceIndex | Select-Object -First 1
            
            if ($activeAdapter -and $activeAdapter.MacAddress) {
                $macAddress = $activeAdapter.MacAddress.Replace("-", "").ToUpper()
                Write-ColorText "✅ MAC Address obtenida del adaptador activo: $($activeAdapter.Name)" "Green"
                Write-ColorText "   Descripción: $($activeAdapter.InterfaceDescription)" "Gray"
                return @{
                    macAddress = $macAddress
                    adapterName = $activeAdapter.Name
                    description = $activeAdapter.InterfaceDescription
                    method = "Active Network Adapter"
                }
            }
        } catch {
            Write-ColorText "⚠️  Get-NetAdapter falló, probando método alternativo..." "Yellow"
        }
        
        # Método 2: Usar WMI para obtener adaptador físico
        try {
            $physicalAdapter = Get-WmiObject -Class Win32_NetworkAdapter | Where-Object { 
                $_.PhysicalAdapter -eq $true -and 
                $_.MACAddress -ne $null -and
                $_.Name -notlike "*Virtual*" -and
                $_.Name -notlike "*Loopback*" -and
                $_.Name -notlike "*Bluetooth*" -and
                $_.Name -notlike "*VMware*" -and
                $_.Name -notlike "*VirtualBox*"
            } | Sort-Object Index | Select-Object -First 1
            
            if ($physicalAdapter -and $physicalAdapter.MACAddress) {
                $macAddress = $physicalAdapter.MACAddress.Replace(":", "").ToUpper()
                Write-ColorText "✅ MAC Address obtenida via WMI: $($physicalAdapter.Name)" "Green"
                return @{
                    macAddress = $macAddress
                    adapterName = $physicalAdapter.Name
                    description = $physicalAdapter.Description
                    method = "WMI Physical Adapter"
                }
            }
        } catch {
            Write-ColorText "⚠️  WMI falló, probando método CIM..." "Yellow"
        }
        
        # Método 3: Usar CIM para obtener adaptador
        try {
            $cimAdapter = Get-CimInstance -ClassName Win32_NetworkAdapter | Where-Object { 
                $_.PhysicalAdapter -eq $true -and 
                $_.MACAddress -ne $null -and
                $_.Name -notlike "*Virtual*" -and
                $_.Name -notlike "*Loopback*" -and
                $_.Name -notlike "*Bluetooth*"
            } | Sort-Object Index | Select-Object -First 1
            
            if ($cimAdapter -and $cimAdapter.MACAddress) {
                $macAddress = $cimAdapter.MACAddress.Replace(":", "").ToUpper()
                Write-ColorText "✅ MAC Address obtenida via CIM: $($cimAdapter.Name)" "Green"
                return @{
                    macAddress = $macAddress
                    adapterName = $cimAdapter.Name
                    description = $cimAdapter.Description
                    method = "CIM Network Adapter"
                }
            }
        } catch {
            Write-ColorText "⚠️  CIM falló, usando método de emergencia..." "Yellow"
        }
        
        # Método 4: Comando getmac como último recurso
        try {
            $getmacOutput = getmac /fo csv /v | ConvertFrom-Csv
            $physicalMAC = $getmacOutput | Where-Object { 
                $_."Physical Address" -ne "N/A" -and 
                $_."Physical Address" -ne "" -and
                $_."Connection Name" -notlike "*Virtual*" -and
                $_."Connection Name" -notlike "*Loopback*" -and
                $_."Connection Name" -notlike "*Bluetooth*"
            } | Select-Object -First 1
            
            if ($physicalMAC -and $physicalMAC."Physical Address") {
                $macAddress = $physicalMAC."Physical Address".Replace("-", "").ToUpper()
                Write-ColorText "✅ MAC Address obtenida via getmac: $($physicalMAC.'Connection Name')" "Green"
                return @{
                    macAddress = $macAddress
                    adapterName = $physicalMAC."Connection Name"
                    description = $physicalMAC."Transport Name"
                    method = "getmac Command"
                }
            }
        } catch {
            Write-ColorText "❌ Todos los métodos fallaron" "Red"
        }
        
        throw "No se pudo obtener MAC Address válida"
        
    } catch {
        Write-ColorText "❌ Error obteniendo MAC Address: $($_.Exception.Message)" "Red"
        throw
    }
}

# Función para guardar MAC Address en archivos para la aplicación
function Save-MACAddressForApp {
    param([hashtable]$MACInfo)
    
    try {
        # Crear archivo JSON con información completa de la MAC
        $macData = @{
            macAddress = $MACInfo.macAddress
            adapterName = $MACInfo.adapterName
            description = $MACInfo.description
            method = $MACInfo.method
            timestamp = (Get-Date).ToString("yyyy-MM-dd HH:mm:ss")
            computerName = $env:COMPUTERNAME
            userName = $env:USERNAME
            isReal = $true
        }
        
        $jsonContent = $macData | ConvertTo-Json -Depth 2
        
        # Guardar en archivo que la aplicación puede leer
        $macFile = Join-Path $PSScriptRoot "mac_address.json"
        $jsonContent | Out-File -FilePath $macFile -Encoding UTF8 -Force
        
        # También guardar en formato simple para compatibilidad
        $MACInfo.macAddress | Out-File -FilePath (Join-Path $PSScriptRoot "mac_address.txt") -Encoding UTF8 -Force
        
        Write-ColorText "✅ MAC Address guardada en archivos para la aplicación" "Green"
        Write-ColorText "   - mac_address.json (información completa)" "Gray"
        Write-ColorText "   - mac_address.txt (solo MAC)" "Gray"
        
    } catch {
        Write-ColorText "❌ Error guardando MAC Address: $($_.Exception.Message)" "Red"
    }
}

# Función principal
function Main {
    Write-ColorText "╔══════════════════════════════════════════════════════════════╗" "Cyan"
    Write-ColorText "║                 BATTLE STRATEGY CREATOR                      ║" "Cyan"
    Write-ColorText "║              Obtener MAC Address Real del Hardware          ║" "Cyan"
    Write-ColorText "╚══════════════════════════════════════════════════════════════╝" "Cyan"
    Write-ColorText ""
    
    # Verificar permisos de administrador
    if (-not (Test-Administrator)) {
        Write-ColorText "⚠️  ADVERTENCIA: No se está ejecutando como administrador" "Yellow"
        Write-ColorText "   Algunos adaptadores pueden no ser detectados correctamente" "Yellow"
        Write-ColorText ""
    } else {
        Write-ColorText "✅ Ejecutándose como administrador" "Green"
        Write-ColorText ""
    }
    
    # Obtener MAC Address del sistema
    try {
        $macInfo = Get-RealMACAddress
        
        Write-ColorText "╔══════════════════════════════════════════════════════════════╗" "Green"
        Write-ColorText "║                  MAC ADDRESS DEL SISTEMA                    ║" "Green"
        Write-ColorText "╚══════════════════════════════════════════════════════════════╝" "Green"
        Write-ColorText ""
        Write-ColorText "🔑 MAC Address: $($macInfo.macAddress)" "White"
        Write-ColorText "📡 Adaptador: $($macInfo.adapterName)" "Gray"
        Write-ColorText "📋 Descripción: $($macInfo.description)" "Gray"
        Write-ColorText "🔧 Método: $($macInfo.method)" "Gray"
        Write-ColorText ""
        
        # Guardar MAC para la aplicación
        Save-MACAddressForApp -MACInfo $macInfo
        
        # Mostrar información adicional del sistema
        Write-ColorText "📊 INFORMACIÓN DEL SISTEMA:" "Cyan"
        Write-ColorText "   Computadora: $env:COMPUTERNAME" "Gray"
        Write-ColorText "   Usuario: $env:USERNAME" "Gray"
        Write-ColorText "   SO: $([System.Environment]::OSVersion.VersionString)" "Gray"
        Write-ColorText "   Fecha: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" "Gray"
        Write-ColorText ""
        
        # Verificar si Node.js está instalado
        Write-ColorText "🔍 Verificando dependencias..." "Yellow"
        
        try {
            $nodeVersion = node --version 2>$null
            if ($nodeVersion) {
                Write-ColorText "✅ Node.js encontrado: $nodeVersion" "Green"
                
                # Verificar si las dependencias están instaladas
                if (Test-Path "node_modules") {
                    Write-ColorText "✅ Dependencias de Node.js instaladas" "Green"
                } else {
                    Write-ColorText "⚠️  Instalando dependencias..." "Yellow"
                    npm install
                }
                
                # Ejecutar la aplicación
                Write-ColorText ""
                Write-ColorText "🚀 INICIANDO BATTLE STRATEGY CREATOR..." "Green"
                Write-ColorText ""
                Write-ColorText "La aplicación se abrirá en: http://localhost:5173" "Cyan"
                Write-ColorText "MAC Address del sistema: $($macInfo.macAddress)" "Cyan"
                Write-ColorText ""
                Write-ColorText "Para generar clave de activación:" "Yellow"
                Write-ColorText "cd generadorkey" "Gray"
                Write-ColorText "npm run generar `"$($macInfo.macAddress)`" `"Lucas2Derepredador2025`"" "Gray"
                Write-ColorText ""
                Write-ColorText "Presiona Ctrl+C para detener la aplicación" "Yellow"
                Write-ColorText ""
                
                # Ejecutar la aplicación
                npm run dev
                
            } else {
                Write-ColorText "❌ Node.js no está instalado" "Red"
                Write-ColorText "Por favor instala Node.js desde: https://nodejs.org/" "Yellow"
            }
        } catch {
            Write-ColorText "❌ Error verificando Node.js: $($_.Exception.Message)" "Red"
        }
        
    } catch {
        Write-ColorText "❌ Error obteniendo MAC Address del sistema: $($_.Exception.Message)" "Red"
        Write-ColorText ""
        Write-ColorText "Posibles soluciones:" "Yellow"
        Write-ColorText "1. Verificar que hay adaptadores de red físicos" "White"
        Write-ColorText "2. Ejecutar como administrador" "White"
        Write-ColorText "3. Verificar drivers de red" "White"
        Write-ColorText "4. Reiniciar el sistema" "White"
    }
    
    Write-ColorText ""
    Read-Host "Presiona Enter para salir"
}

# Ejecutar función principal
Main
