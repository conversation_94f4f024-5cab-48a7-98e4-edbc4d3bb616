// Script para verificar el proceso HMAC paso a paso
import CryptoJS from 'crypto-js';

const getSecretKey = () => {
  const parts = ['Lucas2D', 'r', 'pr', 'dador2025'];
  const vowel = String.fromCharCode(101); // 'e'
  return parts[0] + vowel + parts[1] + vowel + parts[2] + vowel + parts[3];
};

const verifyHMACProcess = (uuid) => {
  console.log('🔍 VERIFICACIÓN DEL PROCESO HMAC');
  console.log('='.repeat(50));
  
  // Paso 1: UUID original
  console.log(`1. UUID original: ${uuid}`);
  
  // Paso 2: Normalizar UUID
  const normalizedUUID = uuid.replace(/-/g, '').toUpperCase();
  console.log(`2. UUID normalizado: ${normalizedUUID}`);
  
  // Paso 3: Clave secreta
  const secretKey = getSecretKey();
  console.log(`3. Clave secreta: ${secretKey}`);
  
  // Paso 4: Generar HMAC SHA256
  const hmac = CryptoJS.HmacSHA256(normalizedUUID, secretKey);
  const hmacHex = hmac.toString(CryptoJS.enc.Hex);
  console.log(`4. HMAC SHA256 completo: ${hmacHex}`);
  console.log(`   Longitud: ${hmacHex.length} caracteres`);
  
  // Paso 5: Tomar primeros 16 caracteres
  const first16 = hmacHex.substring(0, 16);
  console.log(`5. Primeros 16 caracteres: ${first16}`);
  
  // Paso 6: Convertir a mayúsculas
  const upperCase = first16.toUpperCase();
  console.log(`6. En mayúsculas: ${upperCase}`);
  
  // Paso 7: Formatear como clave
  const formattedKey = `${upperCase.substring(0, 4)}-${upperCase.substring(4, 8)}-${upperCase.substring(8, 12)}-${upperCase.substring(12, 16)}`;
  console.log(`7. Clave formateada: ${formattedKey}`);
  
  console.log('='.repeat(50));
  console.log(`✅ RESULTADO FINAL: ${formattedKey}`);
  console.log('='.repeat(50));
  
  return formattedKey;
};

// Probar con diferentes UUIDs
const testUUIDs = [
  '12345678-1234-1234-1234-123456789012',
  '*************-4321-4321-************',
  'ABCDEFGH-ABCD-ABCD-ABCD-ABCDEFGHIJKL'
];

console.log('🧪 PRUEBAS DE VERIFICACIÓN HMAC\n');

testUUIDs.forEach((uuid, index) => {
  console.log(`\nPRUEBA ${index + 1}:`);
  verifyHMACProcess(uuid);
  console.log('\n');
});

// Verificar que solo usamos los primeros 16 dígitos
console.log('📊 ANÁLISIS DE LONGITUDES:');
console.log('='.repeat(50));

const sampleUUID = '12345678-1234-1234-1234-123456789012';
const normalizedUUID = sampleUUID.replace(/-/g, '').toUpperCase();
const secretKey = getSecretKey();
const hmac = CryptoJS.HmacSHA256(normalizedUUID, secretKey).toString(CryptoJS.enc.Hex);

console.log(`HMAC completo: ${hmac.length} caracteres`);
console.log(`Primeros 16: ${hmac.substring(0, 16).length} caracteres`);
console.log(`Clave final: ${'XXXX-XXXX-XXXX-XXXX'.length} caracteres (incluyendo guiones)`);
console.log(`Solo dígitos: ${hmac.substring(0, 16).length} caracteres`);

console.log('\n✅ CONFIRMACIÓN: Se usan exactamente los primeros 16 caracteres del HMAC SHA256');
