@echo off
title Battle Strategy Creator - Compilar para Produccion
color 0C

echo.
echo ========================================
echo   COMPILAR PARA PRODUCCION
echo ========================================
echo.

REM Verificar si Node.js esta instalado
node --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Node.js no esta instalado.
    echo.
    pause
    exit /b 1
)

REM Verificar si las dependencias estan instaladas
if not exist "node_modules" (
    echo ERROR: Las dependencias no estan instaladas.
    echo Ejecuta 'instalar.bat' primero.
    echo.
    pause
    exit /b 1
)

echo Preparando compilacion...
echo.

REM Limpiar compilacion anterior
if exist "dist" (
    echo Limpiando compilacion anterior...
    rmdir /s /q "dist"
)

echo Compilando aplicacion...
echo.
npm run build

if errorlevel 1 (
    echo.
    echo ERROR: La compilacion fallo.
    echo.
    pause
    exit /b 1
)

echo.
echo ========================================
echo   COMPILACION COMPLETADA
echo ========================================
echo.
echo La aplicacion ha sido compilada exitosamente.
echo.
echo Archivos generados en la carpeta 'dist\'
echo.
echo Para desplegar:
echo 1. Sube el contenido de la carpeta 'dist\' a tu servidor web
echo 2. Configura el servidor para servir archivos estaticos
echo 3. Asegurate de que el servidor redirija todas las rutas a index.html
echo.
echo IMPORTANTE:
echo - La aplicacion requiere activacion con UUID
echo - Usa 'generar-clave.bat' para crear claves de activacion
echo - Mantén el generador de claves en un servidor seguro
echo.
echo ========================================
echo.

REM Mostrar tamaño de los archivos
if exist "dist" (
    echo Tamaño de archivos compilados:
    dir "dist" /s
    echo.
)

echo ¿Deseas abrir la carpeta 'dist'? (S/N)
set /p abrir="Respuesta: "

if /i "%abrir%"=="S" (
    explorer "dist"
)

pause
