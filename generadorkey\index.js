// Punto de entrada principal del generador de claves
import readlineSync from 'readline-sync';
import { generarClaveActivacion } from './generador.js';

const mostrarBienvenida = () => {
  console.clear();
  console.log('\x1b[36m\x1b[1m');
  console.log('╔══════════════════════════════════════════════════════════════╗');
  console.log('║                 GENERADOR DE CLAVES DE ACTIVACIÓN            ║');
  console.log('║                    Battle Strategy Creator                   ║');
  console.log('║                         Versión 1.0                         ║');
  console.log('╚══════════════════════════════════════════════════════════════╝');
  console.log('\x1b[0m');
  console.log('');
  console.log('🔐 Sistema de generación de claves basado en HMAC SHA256');
  console.log('📋 Genera claves de 16 dígitos usando UUID del sistema');
  console.log('🛡️  Protección robusta para tu aplicación');
  console.log('');
};

const mostrarMenuPrincipal = () => {
  console.log('\x1b[33mSelecciona el modo de operación:\x1b[0m');
  console.log('');
  console.log('1. 🚀 Modo Rápido - Generar clave directamente');
  console.log('2. 🎛️  Modo Interactivo - Interfaz completa');
  console.log('3. 📖 Ayuda y documentación');
  console.log('4. ❌ Salir');
  console.log('');
};

const modoRapido = () => {
  console.log('\x1b[32m🚀 MODO RÁPIDO - GENERACIÓN DE CLAVE\x1b[0m');
  console.log('═'.repeat(50));
  console.log('');
  
  try {
    // Solicitar UUID
    const uuid = readlineSync.question('📋 UUID del Sistema: ', {
      limit: /^[0-9A-Fa-f-\s]{32,36}$/,
      limitMessage: '\x1b[31m❌ UUID debe tener formato válido\x1b[0m'
    });
    
    // Solicitar clave secreta
    const secretKey = readlineSync.question('🔐 Clave Secreta: ', {
      hideEchoBack: true,
      mask: '*'
    });
    
    // Generar clave
    const claveActivacion = generarClaveActivacion(uuid, secretKey);
    
    console.log('');
    console.log('\x1b[32m✅ CLAVE GENERADA EXITOSAMENTE:\x1b[0m');
    console.log('');
    console.log('\x1b[1m\x1b[36m┌─────────────────────────┐\x1b[0m');
    console.log(`\x1b[1m\x1b[36m│  🔑 ${claveActivacion}  │\x1b[0m`);
    console.log('\x1b[1m\x1b[36m└─────────────────────────┘\x1b[0m');
    console.log('');
    console.log('\x1b[33m⚠️  IMPORTANTE: Guarda esta clave de forma segura\x1b[0m');
    console.log('\x1b[33m📋 Proporciona esta clave al usuario para activar su aplicación\x1b[0m');
    
  } catch (error) {
    console.log('');
    console.log('\x1b[31m❌ ERROR: ' + error.message + '\x1b[0m');
  }
  
  console.log('');
  readlineSync.question('Presiona ENTER para continuar...');
};

const mostrarAyuda = () => {
  console.log('\x1b[34m📖 AYUDA Y DOCUMENTACIÓN\x1b[0m');
  console.log('═'.repeat(50));
  console.log('');
  console.log('\x1b[1m¿Cómo funciona?\x1b[0m');
  console.log('1. El usuario ejecuta la aplicación Battle Strategy Creator');
  console.log('2. La aplicación muestra el UUID único de su sistema');
  console.log('3. El usuario te envía ese UUID');
  console.log('4. Tú usas este generador para crear la clave de activación');
  console.log('5. Le proporcionas la clave al usuario');
  console.log('6. El usuario introduce la clave y activa la aplicación');
  console.log('');
  console.log('\x1b[1mFormatos aceptados:\x1b[0m');
  console.log('• UUID: 12345678-1234-1234-1234-123456789012');
  console.log('• UUID: 12345678123412341234123456789012 (sin guiones)');
  console.log('• Clave: XXXX-XXXX-XXXX-XXXX (16 dígitos hexadecimales)');
  console.log('');
  console.log('\x1b[1mEjemplo:\x1b[0m');
  console.log('UUID: 12345678-1234-1234-1234-123456789012');
  console.log('Clave Secreta: Lucas2Derepredador2025');
  console.log('Resultado: 9511-10EB-3B6E-F379');
  console.log('');
  console.log('\x1b[1mComandos disponibles:\x1b[0m');
  console.log('• npm start          - Ejecutar este menú principal');
  console.log('• npm run interfaz   - Modo interactivo completo');
  console.log('• npm run generar    - Modo línea de comandos');
  console.log('• npm run test       - Ejecutar pruebas');
  console.log('');
  console.log('\x1b[33m⚠️  SEGURIDAD:\x1b[0m');
  console.log('• Mantén la clave secreta segura');
  console.log('• No compartas este generador con usuarios finales');
  console.log('• Cada UUID genera una clave única');
  console.log('• Las claves son válidas indefinidamente');
  console.log('');
  
  readlineSync.question('Presiona ENTER para continuar...');
};

const main = () => {
  let continuar = true;
  
  while (continuar) {
    mostrarBienvenida();
    mostrarMenuPrincipal();
    
    const opcion = readlineSync.question('Ingresa tu opción (1-4): ');
    
    console.log('');
    
    switch (opcion) {
      case '1':
        modoRapido();
        break;
      case '2':
        console.log('🎛️  Iniciando modo interactivo...');
        console.log('');
        // Importar dinámicamente la interfaz
        import('./interfaz.js').then(() => {
          // La interfaz se ejecuta automáticamente
        }).catch(error => {
          console.log('\x1b[31m❌ Error cargando interfaz: ' + error.message + '\x1b[0m');
          readlineSync.question('Presiona ENTER para continuar...');
        });
        return; // Salir del bucle principal
      case '3':
        mostrarAyuda();
        break;
      case '4':
        console.log('\x1b[32m👋 ¡Gracias por usar el Generador de Claves!\x1b[0m');
        console.log('🛡️  Mantén tu aplicación segura');
        continuar = false;
        break;
      default:
        console.log('\x1b[31m❌ Opción inválida. Intenta de nuevo.\x1b[0m');
        readlineSync.question('Presiona ENTER para continuar...');
    }
  }
};

// Ejecutar aplicación principal
main();
