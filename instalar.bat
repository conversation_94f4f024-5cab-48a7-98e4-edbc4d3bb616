@echo off
title Battle Strategy Creator - Instalacion
color 0E

echo.
echo ========================================
echo   BATTLE STRATEGY CREATOR
echo   INSTALACION Y CONFIGURACION
echo ========================================
echo.

REM Verificar si Node.js esta instalado
echo Verificando Node.js...
node --version >nul 2>&1
if errorlevel 1 (
    echo.
    echo ERROR: Node.js no esta instalado.
    echo.
    echo Por favor descarga e instala Node.js desde:
    echo https://nodejs.org/
    echo.
    echo Despues de instalar Node.js, ejecuta este archivo nuevamente.
    echo.
    pause
    exit /b 1
) else (
    echo Node.js encontrado: 
    node --version
)

REM Verificar si npm esta disponible
echo Verificando npm...
npm --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: npm no esta disponible.
    pause
    exit /b 1
) else (
    echo npm encontrado: 
    npm --version
)

echo.
echo ========================================
echo   INSTALANDO DEPENDENCIAS
echo ========================================
echo.

REM Instalar dependencias
echo Instalando dependencias del proyecto...
echo Esto puede tomar unos minutos...
echo.
npm install

if errorlevel 1 (
    echo.
    echo ERROR: No se pudieron instalar las dependencias.
    echo Verifica tu conexion a internet e intenta nuevamente.
    echo.
    pause
    exit /b 1
)

echo.
echo ========================================
echo   CONFIGURACION INICIAL
echo ========================================
echo.

REM Ejecutar script de configuracion
echo Ejecutando configuracion inicial...
echo.
node scripts/setup.js

if errorlevel 1 (
    echo.
    echo ERROR: No se pudo completar la configuracion inicial.
    echo.
    pause
    exit /b 1
)

echo.
echo ========================================
echo   INSTALACION COMPLETADA
echo ========================================
echo.
echo La instalacion se ha completado exitosamente.
echo.
echo ARCHIVOS CREADOS:
echo - ejecutar.bat          (Ejecutar la aplicacion)
echo - generar-clave.bat     (Generar claves de activacion)
echo - example_uuids.txt     (Archivo de ejemplo con UUIDs)
echo - .env                  (Configuracion de entorno)
echo.
echo COMANDOS DISPONIBLES:
echo - npm run dev           (Ejecutar aplicacion)
echo - npm run generate-key  (Generar clave individual)
echo - npm run test-activation (Probar sistema)
echo.
echo PROXIMOS PASOS:
echo 1. Ejecuta 'ejecutar.bat' para iniciar la aplicacion
echo 2. Usa 'generar-clave.bat' para crear claves de activacion
echo 3. Lee PROTECTION_README.md para mas informacion
echo.
echo ========================================
echo.
pause
