// Preload script para Electron
// Este archivo permite la comunicación segura entre el proceso principal y el renderer

const { contextBridge, ipcRenderer } = require('electron');

// Exponer APIs seguras al proceso renderer
contextBridge.exposeInMainWorld('electronAPI', {
  // Función para obtener el UUID del sistema
  getSystemUUID: () => ipcRenderer.invoke('get-system-uuid'),
  
  // Función para verificar si la aplicación está en modo desarrollo
  isDevelopment: () => ipcRenderer.invoke('is-development'),
  
  // Función para obtener información del sistema
  getSystemInfo: () => ipcRenderer.invoke('get-system-info')
});
