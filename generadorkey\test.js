// Pruebas del generador de claves
import { generarClaveActivacion, validarClaveActivacion, generarInformacionDetallada } from './generador.js';

const colores = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

const mostrarTitulo = () => {
  console.log(colores.cyan + colores.bright);
  console.log('╔══════════════════════════════════════════════════════════════╗');
  console.log('║                    PRUEBAS DEL GENERADOR                     ║');
  console.log('║                  Battle Strategy Creator                     ║');
  console.log('╚══════════════════════════════════════════════════════════════╝');
  console.log(colores.reset);
  console.log('');
};

const ejecutarPrueba = (nombre, funcion) => {
  try {
    console.log(`🧪 ${nombre}...`);
    funcion();
    console.log(colores.green + '✅ PASÓ' + colores.reset);
  } catch (error) {
    console.log(colores.red + `❌ FALLÓ: ${error.message}` + colores.reset);
  }
  console.log('');
};

const pruebaGeneracionBasica = () => {
  const uuid = '12345678-1234-1234-1234-123456789012';
  const secretKey = 'Lucas2Derepredador2025';
  const clave = generarClaveActivacion(uuid, secretKey);
  
  if (!clave || typeof clave !== 'string') {
    throw new Error('La clave generada no es válida');
  }
  
  if (!/^[A-F0-9]{4}-[A-F0-9]{4}-[A-F0-9]{4}-[A-F0-9]{4}$/.test(clave)) {
    throw new Error('El formato de la clave no es correcto');
  }
  
  console.log(`   UUID: ${uuid}`);
  console.log(`   Clave: ${clave}`);
};

const pruebaConsistencia = () => {
  const uuid = '12345678-1234-1234-1234-123456789012';
  const secretKey = 'Lucas2Derepredador2025';
  
  const clave1 = generarClaveActivacion(uuid, secretKey);
  const clave2 = generarClaveActivacion(uuid, secretKey);
  
  if (clave1 !== clave2) {
    throw new Error('Las claves generadas no son consistentes');
  }
  
  console.log(`   Clave 1: ${clave1}`);
  console.log(`   Clave 2: ${clave2}`);
};

const pruebaUUIDsDiferentes = () => {
  const uuid1 = '12345678-1234-1234-1234-123456789012';
  const uuid2 = '*************-4321-4321-************';
  const secretKey = 'Lucas2Derepredador2025';
  
  const clave1 = generarClaveActivacion(uuid1, secretKey);
  const clave2 = generarClaveActivacion(uuid2, secretKey);
  
  if (clave1 === clave2) {
    throw new Error('UUIDs diferentes generan la misma clave');
  }
  
  console.log(`   UUID 1: ${uuid1} -> ${clave1}`);
  console.log(`   UUID 2: ${uuid2} -> ${clave2}`);
};

const pruebaValidacion = () => {
  const uuid = '12345678-1234-1234-1234-123456789012';
  const secretKey = 'Lucas2Derepredador2025';
  const clave = generarClaveActivacion(uuid, secretKey);
  
  const esValida = validarClaveActivacion(uuid, secretKey, clave);
  
  if (!esValida) {
    throw new Error('La validación de clave falló');
  }
  
  // Probar con clave incorrecta
  const esInvalida = validarClaveActivacion(uuid, secretKey, '0000-0000-0000-0000');
  
  if (esInvalida) {
    throw new Error('La validación aceptó una clave incorrecta');
  }
  
  console.log(`   Clave válida: ${clave} -> ${esValida}`);
  console.log(`   Clave inválida: 0000-0000-0000-0000 -> ${esInvalida}`);
};

const pruebaFormatos = () => {
  const secretKey = 'Lucas2Derepredador2025';
  
  // UUID con guiones
  const uuid1 = '12345678-1234-1234-1234-123456789012';
  const clave1 = generarClaveActivacion(uuid1, secretKey);
  
  // UUID sin guiones
  const uuid2 = '12345678123412341234123456789012';
  const clave2 = generarClaveActivacion(uuid2, secretKey);
  
  if (clave1 !== clave2) {
    throw new Error('Formatos de UUID diferentes generan claves diferentes');
  }
  
  console.log(`   Con guiones: ${uuid1} -> ${clave1}`);
  console.log(`   Sin guiones: ${uuid2} -> ${clave2}`);
};

const pruebaErrores = () => {
  const secretKey = 'Lucas2Derepredador2025';
  
  // UUID vacío
  try {
    generarClaveActivacion('', secretKey);
    throw new Error('Debería haber fallado con UUID vacío');
  } catch (error) {
    if (!error.message.includes('UUID es requerido')) {
      throw error;
    }
  }
  
  // Clave secreta vacía
  try {
    generarClaveActivacion('12345678-1234-1234-1234-123456789012', '');
    throw new Error('Debería haber fallado con clave secreta vacía');
  } catch (error) {
    if (!error.message.includes('Clave secreta es requerida')) {
      throw error;
    }
  }
  
  // UUID inválido
  try {
    generarClaveActivacion('uuid-invalido', secretKey);
    throw new Error('Debería haber fallado con UUID inválido');
  } catch (error) {
    if (!error.message.includes('UUID debe tener formato válido')) {
      throw error;
    }
  }
  
  console.log('   ✅ Manejo de errores correcto');
};

const pruebaInformacionDetallada = () => {
  const uuid = '12345678-1234-1234-1234-123456789012';
  const secretKey = 'Lucas2Derepredador2025';
  
  const info = generarInformacionDetallada(uuid, secretKey);
  
  if (!info.uuidOriginal || !info.uuidNormalizado || !info.hmacCompleto || !info.claveActivacion) {
    throw new Error('Información detallada incompleta');
  }
  
  if (info.hmacCompleto.length !== 64) {
    throw new Error('HMAC SHA256 debe tener 64 caracteres');
  }
  
  if (info.primeros16Digitos.length !== 16) {
    throw new Error('Primeros 16 dígitos deben tener exactamente 16 caracteres');
  }
  
  console.log(`   HMAC completo: ${info.hmacCompleto.substring(0, 20)}...`);
  console.log(`   Primeros 16: ${info.primeros16Digitos}`);
  console.log(`   Clave final: ${info.claveActivacion}`);
};

const pruebaEjemplosConocidos = () => {
  // Casos de prueba con resultados conocidos
  const casos = [
    {
      uuid: '12345678-1234-1234-1234-123456789012',
      secretKey: 'Lucas2Derepredador2025',
      claveEsperada: '9511-10EB-3B6E-F379'
    },
    {
      uuid: '*************-4321-4321-************',
      secretKey: 'Lucas2Derepredador2025',
      claveEsperada: 'E825-1A0B-FE9C-FF93'
    }
  ];
  
  casos.forEach((caso, index) => {
    const claveGenerada = generarClaveActivacion(caso.uuid, caso.secretKey);
    
    if (claveGenerada !== caso.claveEsperada) {
      throw new Error(`Caso ${index + 1}: Esperado ${caso.claveEsperada}, obtenido ${claveGenerada}`);
    }
    
    console.log(`   Caso ${index + 1}: ${caso.uuid} -> ${claveGenerada} ✅`);
  });
};

const main = () => {
  mostrarTitulo();
  
  console.log(colores.yellow + '🧪 EJECUTANDO SUITE DE PRUEBAS' + colores.reset);
  console.log('═'.repeat(50));
  console.log('');
  
  ejecutarPrueba('Test 1: Generación básica', pruebaGeneracionBasica);
  ejecutarPrueba('Test 2: Consistencia', pruebaConsistencia);
  ejecutarPrueba('Test 3: UUIDs diferentes', pruebaUUIDsDiferentes);
  ejecutarPrueba('Test 4: Validación', pruebaValidacion);
  ejecutarPrueba('Test 5: Formatos de UUID', pruebaFormatos);
  ejecutarPrueba('Test 6: Manejo de errores', pruebaErrores);
  ejecutarPrueba('Test 7: Información detallada', pruebaInformacionDetallada);
  ejecutarPrueba('Test 8: Ejemplos conocidos', pruebaEjemplosConocidos);
  
  console.log('═'.repeat(50));
  console.log(colores.green + colores.bright + '🎉 TODAS LAS PRUEBAS COMPLETADAS' + colores.reset);
  console.log('');
  console.log('✅ El generador de claves funciona correctamente');
  console.log('🔐 Sistema HMAC SHA256 validado');
  console.log('📋 Formato de claves verificado');
  console.log('🛡️  Listo para uso en producción');
  console.log('');
};

// Ejecutar pruebas
main();
