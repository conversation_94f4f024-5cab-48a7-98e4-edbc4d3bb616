@echo off
title Battle Strategy Creator - MAC Address Real
color 0A

echo.
echo ========================================
echo   BATTLE STRATEGY CREATOR
echo   Obtener MAC Address Real
echo ========================================
echo.

REM Verificar si PowerShell está disponible
powershell -Command "Write-Host 'PowerShell disponible'" >nul 2>&1
if errorlevel 1 (
    echo ERROR: PowerShell no está disponible en este sistema
    echo Por favor instala PowerShell o usa Windows 10/11
    pause
    exit /b 1
)

echo 🔍 Verificando permisos de administrador...

REM Verificar si se está ejecutando como administrador
net session >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ Ejecutándose como administrador
    echo.
    echo 🚀 Iniciando script PowerShell para obtener MAC Address...
    echo.
    
    REM Ejecutar el script PowerShell
    powershell -ExecutionPolicy Bypass -File "ejecutar.ps1"
    
) else (
    echo ⚠️  No se está ejecutando como administrador
    echo    (Algunos adaptadores pueden no detectarse correctamente)
    echo.
    echo 🔄 Intentando ejecutar sin permisos de administrador...
    echo.
    
    REM Ejecutar sin permisos de administrador
    powershell -ExecutionPolicy Bypass -File "ejecutar.ps1"
    
    if errorlevel 1 (
        echo.
        echo ⚠️  Algunos adaptadores pueden no haberse detectado
        echo.
        echo 💡 Para mejores resultados:
        echo.
        echo 1. Clic derecho en "ejecutar-admin.bat"
        echo 2. Seleccionar "Ejecutar como administrador"
        echo.
        echo O alternativamente:
        echo 1. Abrir PowerShell como administrador
        echo 2. Navegar a esta carpeta
        echo 3. Ejecutar: .\ejecutar.ps1
        echo.
        pause
    )
)

echo.
echo ========================================
echo   Proceso completado
echo ========================================
echo.
pause
