// Script para gestionar el UUID del sistema
// Útil para desarrollo y diagnóstico

import { getSystemUUID, generateConsistentUUID } from '../utils/crypto.js';

const showCurrentUUID = async () => {
  console.log('📋 UUID ACTUAL DEL SISTEMA');
  console.log('═'.repeat(50));
  console.log('');
  
  try {
    // Mostrar UUID desde cache
    const cachedUUID = localStorage.getItem('system_uuid_cache');
    if (cachedUUID) {
      console.log(`💾 UUID en cache: ${cachedUUID}`);
    } else {
      console.log('💾 No hay UUID en cache');
    }
    
    // Generar UUID consistente
    const consistentUUID = generateConsistentUUID();
    console.log(`🔧 UUID consistente: ${consistentUUID}`);
    
    // Obtener UUID usando la función principal
    const systemUUID = await getSystemUUID();
    console.log(`🖥️  UUID del sistema: ${systemUUID}`);
    
    console.log('');
    console.log('ℹ️  El UUID del sistema debería ser siempre el mismo');
    console.log('   para este navegador/dispositivo específico.');
    
  } catch (error) {
    console.error('❌ Error obteniendo UUID:', error.message);
  }
  
  console.log('');
  console.log('═'.repeat(50));
};

const clearUUIDCache = () => {
  console.log('🗑️  LIMPIAR CACHE DE UUID');
  console.log('═'.repeat(50));
  console.log('');
  
  try {
    const cachedUUID = localStorage.getItem('system_uuid_cache');
    
    if (cachedUUID) {
      localStorage.removeItem('system_uuid_cache');
      console.log(`✅ UUID removido del cache: ${cachedUUID}`);
      console.log('🔄 El próximo acceso generará un nuevo UUID consistente');
    } else {
      console.log('ℹ️  No hay UUID en cache para limpiar');
    }
    
  } catch (error) {
    console.error('❌ Error limpiando cache:', error.message);
  }
  
  console.log('');
  console.log('═'.repeat(50));
};

const regenerateUUID = async () => {
  console.log('🔄 REGENERAR UUID DEL SISTEMA');
  console.log('═'.repeat(50));
  console.log('');
  
  try {
    // Limpiar cache actual
    const oldUUID = localStorage.getItem('system_uuid_cache');
    localStorage.removeItem('system_uuid_cache');
    
    if (oldUUID) {
      console.log(`🗑️  UUID anterior: ${oldUUID}`);
    }
    
    // Generar nuevo UUID
    const newUUID = await getSystemUUID();
    console.log(`✨ Nuevo UUID: ${newUUID}`);
    
    console.log('');
    console.log('⚠️  IMPORTANTE: Si cambias el UUID, necesitarás');
    console.log('   una nueva clave de activación para este UUID.');
    
  } catch (error) {
    console.error('❌ Error regenerando UUID:', error.message);
  }
  
  console.log('');
  console.log('═'.repeat(50));
};

const validateUUIDConsistency = async () => {
  console.log('🔍 VALIDAR CONSISTENCIA DE UUID');
  console.log('═'.repeat(50));
  console.log('');
  
  try {
    // Obtener UUID múltiples veces
    const uuid1 = await getSystemUUID();
    const uuid2 = await getSystemUUID();
    const uuid3 = await getSystemUUID();
    
    console.log(`1️⃣  Primera llamada: ${uuid1}`);
    console.log(`2️⃣  Segunda llamada: ${uuid2}`);
    console.log(`3️⃣  Tercera llamada: ${uuid3}`);
    
    console.log('');
    
    if (uuid1 === uuid2 && uuid2 === uuid3) {
      console.log('✅ CONSISTENCIA VERIFICADA');
      console.log('   El UUID es el mismo en todas las llamadas');
    } else {
      console.log('❌ INCONSISTENCIA DETECTADA');
      console.log('   El UUID cambia entre llamadas (problema!)');
    }
    
  } catch (error) {
    console.error('❌ Error validando consistencia:', error.message);
  }
  
  console.log('');
  console.log('═'.repeat(50));
};

const showSystemFingerprint = () => {
  console.log('🔍 FINGERPRINT DEL SISTEMA');
  console.log('═'.repeat(50));
  console.log('');
  
  try {
    console.log('📊 Características del sistema:');
    console.log(`   User Agent: ${navigator.userAgent.substring(0, 50)}...`);
    console.log(`   Idioma: ${navigator.language}`);
    console.log(`   Pantalla: ${screen.width}x${screen.height}`);
    console.log(`   Profundidad de color: ${screen.colorDepth}`);
    console.log(`   Zona horaria: UTC${new Date().getTimezoneOffset() / -60}`);
    console.log(`   Plataforma: ${navigator.platform}`);
    console.log(`   CPU cores: ${navigator.hardwareConcurrency || 'No disponible'}`);
    
    console.log('');
    console.log('ℹ️  Estas características se usan para generar');
    console.log('   un UUID consistente para este sistema.');
    
  } catch (error) {
    console.error('❌ Error obteniendo fingerprint:', error.message);
  }
  
  console.log('');
  console.log('═'.repeat(50));
};

const main = () => {
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    console.log('');
    console.log('🔧 HERRAMIENTA DE GESTIÓN DE UUID');
    console.log('═'.repeat(50));
    console.log('');
    console.log('Uso: node manageUUID.js <comando>');
    console.log('');
    console.log('Comandos disponibles:');
    console.log('  show        - Mostrar UUID actual');
    console.log('  clear       - Limpiar cache de UUID');
    console.log('  regenerate  - Regenerar UUID del sistema');
    console.log('  validate    - Validar consistencia de UUID');
    console.log('  fingerprint - Mostrar características del sistema');
    console.log('  help        - Mostrar esta ayuda');
    console.log('');
    console.log('Ejemplos:');
    console.log('  node manageUUID.js show');
    console.log('  node manageUUID.js clear');
    console.log('  node manageUUID.js validate');
    console.log('');
    console.log('💡 Para usar en navegador:');
    console.log('  1. Abrir DevTools (F12)');
    console.log('  2. Ir a Console');
    console.log('  3. Ejecutar: localStorage.getItem("system_uuid_cache")');
    console.log('');
    process.exit(1);
  }
  
  const command = args[0].toLowerCase();
  
  switch (command) {
    case 'show':
      showCurrentUUID();
      break;
    case 'clear':
      clearUUIDCache();
      break;
    case 'regenerate':
      regenerateUUID();
      break;
    case 'validate':
      validateUUIDConsistency();
      break;
    case 'fingerprint':
      showSystemFingerprint();
      break;
    case 'help':
      main(); // Mostrar ayuda
      break;
    default:
      console.log(`❌ Comando desconocido: ${command}`);
      console.log('💡 Usa "help" para ver comandos disponibles');
      process.exit(1);
  }
};

// Ejecutar si es llamado directamente
if (import.meta.url.includes(process.argv[1])) {
  main();
}

export { showCurrentUUID, clearUUIDCache, regenerateUUID, validateUUIDConsistency };
