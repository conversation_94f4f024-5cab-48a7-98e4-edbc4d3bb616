@echo off
title Battle Strategy Creator - Menu Principal
color 0A

:MENU_PRINCIPAL
cls
echo.
echo ========================================
echo   BATTLE STRATEGY CREATOR
echo   MENU PRINCIPAL
echo ========================================
echo.
echo Version: 1.0.0
echo Protegido con HMAC SHA256
echo.
echo ========================================
echo.
echo Selecciona una opcion:
echo.
echo 1. Ejecutar Aplicacion
echo 2. Generar Claves de Activacion
echo 3. Instalar/Configurar Proyecto
echo 4. Verificar Sistema
echo 5. Abrir Documentacion
echo 6. Salir
echo.
echo ========================================
echo.
set /p opcion="Ingresa tu opcion (1-6): "

if "%opcion%"=="1" goto EJECUTAR_APP
if "%opcion%"=="2" goto GENERAR_CLAVES
if "%opcion%"=="3" goto INSTALAR
if "%opcion%"=="4" goto VERIFICAR
if "%opcion%"=="5" goto DOCUMENTACION
if "%opcion%"=="6" goto SALIR

echo.
echo Opcion invalida. Presiona cualquier tecla para continuar...
pause >nul
goto MENU_PRINCIPAL

:EJECUTAR_APP
cls
echo.
echo ========================================
echo   EJECUTANDO APLICACION
echo ========================================
echo.

REM Verificar si esta instalado
if not exist "node_modules" (
    echo El proyecto no esta instalado.
    echo Ejecutando instalacion automatica...
    echo.
    call instalar.bat
    if errorlevel 1 (
        echo Error en la instalacion.
        pause
        goto MENU_PRINCIPAL
    )
)

call ejecutar.bat
pause
goto MENU_PRINCIPAL

:GENERAR_CLAVES
cls
echo.
echo ========================================
echo   GENERADOR DE CLAVES
echo ========================================
echo.
call generar-clave.bat
goto MENU_PRINCIPAL

:INSTALAR
cls
echo.
echo ========================================
echo   INSTALACION Y CONFIGURACION
echo ========================================
echo.
call instalar.bat
pause
goto MENU_PRINCIPAL

:VERIFICAR
cls
echo.
echo ========================================
echo   VERIFICACION DEL SISTEMA
echo ========================================
echo.

REM Verificar Node.js
echo Verificando Node.js...
node --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Node.js no esta instalado
) else (
    echo [OK] Node.js instalado: 
    node --version
)

REM Verificar npm
echo Verificando npm...
npm --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] npm no esta disponible
) else (
    echo [OK] npm disponible: 
    npm --version
)

REM Verificar dependencias
echo Verificando dependencias...
if exist "node_modules" (
    echo [OK] Dependencias instaladas
) else (
    echo [ERROR] Dependencias no instaladas
)

REM Verificar archivos principales
echo Verificando archivos del proyecto...
if exist "App.tsx" (
    echo [OK] App.tsx encontrado
) else (
    echo [ERROR] App.tsx no encontrado
)

if exist "utils\crypto.ts" (
    echo [OK] Sistema de cifrado encontrado
) else (
    echo [ERROR] Sistema de cifrado no encontrado
)

if exist "scripts\generateActivationKey.js" (
    echo [OK] Generador de claves encontrado
) else (
    echo [ERROR] Generador de claves no encontrado
)

echo.
echo Ejecutando pruebas del sistema...
echo.
if exist "node_modules" (
    node scripts/testActivation.js
) else (
    echo No se pueden ejecutar las pruebas. Instala las dependencias primero.
)

echo.
echo ========================================
pause
goto MENU_PRINCIPAL

:DOCUMENTACION
cls
echo.
echo ========================================
echo   DOCUMENTACION
echo ========================================
echo.
echo Abriendo archivos de documentacion...
echo.

if exist "PROTECTION_README.md" (
    echo Abriendo PROTECTION_README.md...
    start notepad "PROTECTION_README.md"
) else (
    echo PROTECTION_README.md no encontrado
)

if exist "docs\DEVELOPER_GUIDE.md" (
    echo Abriendo DEVELOPER_GUIDE.md...
    start notepad "docs\DEVELOPER_GUIDE.md"
) else (
    echo DEVELOPER_GUIDE.md no encontrado
)

echo.
echo Archivos de documentacion abiertos en Notepad.
echo.
pause
goto MENU_PRINCIPAL

:SALIR
cls
echo.
echo ========================================
echo   BATTLE STRATEGY CREATOR
echo ========================================
echo.
echo Gracias por usar Battle Strategy Creator!
echo.
echo Desarrollado con sistema de proteccion HMAC SHA256
echo UUID-based activation system
echo.
echo ========================================
echo.
timeout /t 3 >nul
exit /b 0
