import { useState, useEffect } from 'react';
import { validateActivationKey, generateTestUUID } from '../utils/crypto';
import { getProtectionConfig } from '../config/protection';

interface UseActivationReturn {
  isActivated: boolean;
  isLoading: boolean;
  checkActivation: () => Promise<void>;
  deactivate: () => void;
}

export const useActivation = (): UseActivationReturn => {
  const [isActivated, setIsActivated] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  useEffect(() => {
    checkActivation();
  }, []);

  const checkActivation = async (): Promise<void> => {
    try {
      setIsLoading(true);
      const config = getProtectionConfig();

      // Verificar si la aplicación está activada
      const activationStatus = localStorage.getItem(config.storageKeys.activated);
      const storedUUID = localStorage.getItem(config.storageKeys.uuid);

      if (activationStatus === 'true' && storedUUID) {
        // Verificación adicional: comprobar que la activación sigue siendo válida
        // Esto previene manipulación del localStorage
        try {
          // Obtener UUID actual del sistema (en desarrollo usa UUID de prueba)
          const currentUUID = config.developmentMode ? generateTestUUID() : storedUUID;

          // En modo desarrollo, aceptar cualquier activación válida
          // En producción, podrías agregar validación adicional aquí
          if (config.developmentMode || storedUUID === currentUUID) {
            setIsActivated(true);
          } else {
            // UUID cambió, requerir nueva activación
            console.warn('UUID del sistema cambió, requiere nueva activación');
            localStorage.removeItem(config.storageKeys.activated);
            localStorage.removeItem(config.storageKeys.uuid);
            setIsActivated(false);
          }
        } catch (validationError) {
          console.error('Error en validación adicional:', validationError);
          setIsActivated(true); // Mantener activación en caso de error de validación
        }
      } else {
        setIsActivated(false);
      }
    } catch (error) {
      console.error('Error verificando activación:', error);
      setIsActivated(false);
    } finally {
      setIsLoading(false);
    }
  };

  const deactivate = (): void => {
    const config = getProtectionConfig();
    localStorage.removeItem(config.storageKeys.activated);
    localStorage.removeItem(config.storageKeys.uuid);
    localStorage.removeItem(config.storageKeys.attempts);
    localStorage.removeItem(config.storageKeys.lastAttempt);
    setIsActivated(false);
  };

  return {
    isActivated,
    isLoading,
    checkActivation,
    deactivate
  };
};
