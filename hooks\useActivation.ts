import { useState, useEffect } from 'react';
import { validateActivationKey } from '../utils/crypto';

interface UseActivationReturn {
  isActivated: boolean;
  isLoading: boolean;
  checkActivation: () => Promise<void>;
  deactivate: () => void;
}

export const useActivation = (): UseActivationReturn => {
  const [isActivated, setIsActivated] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  useEffect(() => {
    checkActivation();
  }, []);

  const checkActivation = async (): Promise<void> => {
    try {
      setIsLoading(true);
      
      // Verificar si la aplicación está activada
      const activationStatus = localStorage.getItem('app_activated');
      const storedUUID = localStorage.getItem('activation_uuid');
      
      if (activationStatus === 'true' && storedUUID) {
        // Verificar que la activación sigue siendo válida
        // En una implementación más robusta, podr<PERSON> re-validar contra el UUID actual
        setIsActivated(true);
      } else {
        setIsActivated(false);
      }
    } catch (error) {
      console.error('Error verificando activación:', error);
      setIsActivated(false);
    } finally {
      setIsLoading(false);
    }
  };

  const deactivate = (): void => {
    localStorage.removeItem('app_activated');
    localStorage.removeItem('activation_uuid');
    setIsActivated(false);
  };

  return {
    isActivated,
    isLoading,
    checkActivation,
    deactivate
  };
};
