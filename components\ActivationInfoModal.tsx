import React, { useState, useEffect } from 'react';
import { generateTestUUID } from '../utils/crypto';
import { getProtectionConfig } from '../config/protection';

interface ActivationInfoModalProps {
  isOpen: boolean;
  onClose: () => void;
  onDeactivate: () => void;
}

const ActivationInfoModal: React.FC<ActivationInfoModalProps> = ({ 
  isOpen, 
  onClose, 
  onDeactivate 
}) => {
  const [activationInfo, setActivationInfo] = useState<{
    uuid: string;
    activationKey: string;
    activatedDate: string;
    isActivated: boolean;
  } | null>(null);
  const [showConfirmDelete, setShowConfirmDelete] = useState(false);

  useEffect(() => {
    if (isOpen) {
      loadActivationInfo();
    }
  }, [isOpen]);

  const loadActivationInfo = () => {
    try {
      const config = getProtectionConfig();
      const isActivated = localStorage.getItem(config.storageKeys.activated) === 'true';
      const storedUUID = localStorage.getItem(config.storageKeys.uuid);

      // Obtener UUID actual del sistema
      let currentUUID = 'No disponible';
      if (config.developmentMode) {
        // En desarrollo, generar UUID de prueba consistente
        currentUUID = generateTestUUID();
      } else if (storedUUID) {
        currentUUID = storedUUID;
      }

      if (isActivated && storedUUID) {
        setActivationInfo({
          uuid: currentUUID,
          activationKey: 'XXXX-XXXX-XXXX-XXXX', // Por seguridad, no mostrar la clave real
          activatedDate: new Date().toLocaleDateString(), // Podrías almacenar la fecha real
          isActivated: true
        });
      } else {
        // Mostrar UUID actual incluso si no está activado (para facilitar activación)
        setActivationInfo({
          uuid: currentUUID,
          activationKey: 'No activado',
          activatedDate: 'No activado',
          isActivated: false
        });
      }
    } catch (error) {
      console.error('Error cargando información de activación:', error);
      setActivationInfo(null);
    }
  };

  const handleCopyUUID = () => {
    if (activationInfo?.uuid && activationInfo.uuid !== 'No disponible') {
      navigator.clipboard.writeText(activationInfo.uuid).then(() => {
        // Mostrar feedback visual (opcional)
        alert('UUID copiado al portapapeles');
      }).catch(err => {
        console.error('Error al copiar UUID:', err);
      });
    }
  };

  const handleDeactivate = () => {
    if (showConfirmDelete) {
      onDeactivate();
      setShowConfirmDelete(false);
      onClose();
    } else {
      setShowConfirmDelete(true);
    }
  };

  const handleCancelDelete = () => {
    setShowConfirmDelete(false);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-gray-800 rounded-lg shadow-xl max-w-md w-full mx-4 p-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center">
            <div className="bg-blue-600 p-2 rounded-lg mr-3">
              <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 7a2 2 0 012 2m0 0a2 2 0 012 2m-2-2a2 2 0 00-2 2m2-2V5a2 2 0 00-2-2H9a2 2 0 00-2 2v2M7 7a2 2 0 012-2m0 0a2 2 0 012 2m-2-2a2 2 0 00-2 2m2-2V5a2 2 0 00-2 2" />
              </svg>
            </div>
            <h2 className="text-xl font-bold text-white">Información de Activación</h2>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white transition-colors"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Content */}
        <div className="space-y-4">
          {/* Estado de Activación */}
          <div className="bg-gray-700 p-4 rounded-lg">
            <div className="flex items-center justify-between">
              <span className="text-gray-300">Estado:</span>
              <div className="flex items-center">
                {activationInfo?.isActivated ? (
                  <>
                    <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                    <span className="text-green-400 font-semibold">ACTIVADO</span>
                  </>
                ) : (
                  <>
                    <div className="w-3 h-3 bg-red-500 rounded-full mr-2"></div>
                    <span className="text-red-400 font-semibold">NO ACTIVADO</span>
                  </>
                )}
              </div>
            </div>
          </div>

          {/* UUID del Sistema */}
          <div className="bg-gray-700 p-4 rounded-lg">
            <div className="flex items-center justify-between mb-2">
              <span className="text-gray-300 font-medium">UUID del Sistema:</span>
              {activationInfo?.uuid && activationInfo.uuid !== 'No disponible' && (
                <button
                  onClick={handleCopyUUID}
                  className="text-blue-400 hover:text-blue-300 text-sm"
                  title="Copiar UUID"
                >
                  📋 Copiar
                </button>
              )}
            </div>
            <div className="bg-gray-800 p-3 rounded border font-mono text-sm text-gray-200 break-all">
              {activationInfo?.uuid || 'Cargando...'}
            </div>
          </div>

          {/* Clave de Activación */}
          <div className="bg-gray-700 p-4 rounded-lg">
            <span className="text-gray-300 font-medium block mb-2">Clave de Activación:</span>
            <div className="bg-gray-800 p-3 rounded border font-mono text-sm text-gray-200">
              {activationInfo?.isActivated ? (
                <span className="text-green-400">●●●●-●●●●-●●●●-●●●● (Oculta por seguridad)</span>
              ) : (
                <span className="text-red-400">No activado</span>
              )}
            </div>
          </div>

          {/* Fecha de Activación */}
          {activationInfo?.isActivated && (
            <div className="bg-gray-700 p-4 rounded-lg">
              <span className="text-gray-300 font-medium block mb-2">Fecha de Activación:</span>
              <div className="text-gray-200">
                {activationInfo.activatedDate}
              </div>
            </div>
          )}

          {/* Ayuda Rápida */}
          {!activationInfo?.isActivated && (
            <div className="bg-yellow-900 border border-yellow-700 p-4 rounded-lg">
              <h3 className="text-yellow-200 font-medium mb-2">💡 ¿Necesitas activar?</h3>
              <div className="text-sm text-yellow-300 space-y-1">
                <p>1. Copia el UUID de arriba</p>
                <p>2. Envíalo al administrador del sistema</p>
                <p>3. Recibe tu clave de activación</p>
                <p>4. Cierra esta ventana y activa la aplicación</p>
              </div>
            </div>
          )}

          {/* Información Técnica */}
          <div className="bg-blue-900 border border-blue-700 p-4 rounded-lg">
            <h3 className="text-blue-200 font-medium mb-2">ℹ️ Información Técnica</h3>
            <div className="text-sm text-blue-300 space-y-1">
              <p>• Protección: HMAC SHA256</p>
              <p>• Formato de clave: 16 dígitos hexadecimales</p>
              <p>• Activación: Basada en UUID único del sistema</p>
              <p>• Persistencia: LocalStorage del navegador</p>
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="flex justify-between mt-6">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded transition-colors"
          >
            Cerrar
          </button>
          
          {activationInfo?.isActivated && (
            <div className="space-x-2">
              {showConfirmDelete ? (
                <>
                  <button
                    onClick={handleCancelDelete}
                    className="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded transition-colors"
                  >
                    Cancelar
                  </button>
                  <button
                    onClick={handleDeactivate}
                    className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded transition-colors"
                  >
                    ⚠️ Confirmar Borrado
                  </button>
                </>
              ) : (
                <button
                  onClick={handleDeactivate}
                  className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded transition-colors"
                >
                  🗑️ Borrar Activación
                </button>
              )}
            </div>
          )}
        </div>

        {/* Warning */}
        {showConfirmDelete && (
          <div className="mt-4 p-3 bg-red-900 border border-red-700 rounded-lg">
            <p className="text-red-200 text-sm">
              ⚠️ <strong>Advertencia:</strong> Al borrar la activación, necesitarás una nueva clave para volver a usar la aplicación.
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default ActivationInfoModal;
