# 🛡️ Verificación de Licencia - Battle Strategy Creator

## 🔍 **Proceso de Verificación**

Battle Strategy Creator ahora **verifica la licencia ANTES de abrir** la aplicación principal. Esto garantiza que solo usuarios autorizados puedan acceder al software.

---

## 🚀 **Flujo de Inicio**

### **1. Usuario ejecuta la aplicación**
```
🔄 Verificando licencia del sistema...
🔍 Comprobando estado de activación
🛡️ Validando protección HMAC SHA256
📋 Verificando UUID del sistema
```

### **2. Tres posibles resultados:**

#### ✅ **ACTIVADO** - Acceso directo
- La aplicación se abre inmediatamente
- Usuario puede usar todas las funciones
- No se requiere acción adicional

#### ❌ **NO ACTIVADO** - Pantalla de activación
- Se muestra la pantalla de verificación de licencia
- Usuario debe obtener clave de activación
- Proceso de activación requerido

#### ⚠️ **ERROR** - Problema de verificación
- Se muestra pantalla de activación por seguridad
- Posible manipulación del sistema detectada
- Requiere nueva activación

---

## 🔐 **Pantalla de Activación**

### **Información mostrada:**
```
╔══════════════════════════════════════╗
║        Battle Strategy Creator       ║
║     Verificación de Licencia         ║
║                                      ║
║ 🛡️ Sistema protegido con HMAC SHA256 ║
║ 🔑 Activación basada en UUID         ║
╚══════════════════════════════════════╝

UUID del Sistema: ************************************
[📋 Copiar]

📧 Envía este UUID al administrador

📋 Proceso de activación:
1. Copia el UUID de arriba
2. Envíalo al administrador del sistema  
3. Recibe tu clave de activación personalizada
4. Introdúcela abajo y presiona "Activar"

Clave de Activación: [____-____-____-____]
[Activar]
```

---

## 👨‍💼 **Para Administradores**

### **Cuando un usuario solicita activación:**

1. **Usuario te contacta:**
   ```
   "Necesito activar Battle Strategy Creator"
   "Mi UUID es: ************************************"
   ```

2. **Tú generas la clave:**
   ```bash
   cd generadorkey
   npm run generar "************************************" "Lucas2Derepredador2025"
   # Resultado: 9511-10EB-3B6E-F379
   ```

3. **Proporcionas la clave:**
   ```
   "Tu clave de activación es: 9511-10EB-3B6E-F379"
   "Introdúcela en la aplicación y presiona Activar"
   ```

4. **Confirmación:**
   ```
   Usuario: "¡Funcionó! La aplicación está activada"
   ```

---

## 🔧 **Para Desarrolladores**

### **Probar el sistema de verificación:**

#### **Resetear activación (para pruebas):**
```bash
# Método 1: Script
npm run reset-activation reset

# Método 2: DevTools Console
localStorage.removeItem('app_activated');
localStorage.removeItem('activation_uuid');
location.reload();
```

#### **Verificar estado actual:**
```bash
npm run reset-activation status
```

#### **Probar flujo completo:**
```bash
# 1. Resetear activación
npm run reset-activation reset

# 2. Ejecutar aplicación
npm run dev

# 3. Debería mostrar pantalla de activación
# 4. Generar clave con UUID mostrado
cd generadorkey
npm run generar "UUID-MOSTRADO" "Lucas2Derepredador2025"

# 5. Introducir clave en aplicación
# 6. Verificar que se activa correctamente
```

---

## 🛡️ **Medidas de Seguridad Implementadas**

### ✅ **Verificación al inicio:**
- **Antes** de mostrar la aplicación principal
- **Validación** de estado de activación en localStorage
- **Comprobación** de integridad de datos

### ✅ **Protección contra manipulación:**
- **Detección** de cambios en UUID del sistema
- **Invalidación** automática si se detectan inconsistencias
- **Re-activación** requerida en caso de problemas

### ✅ **Experiencia de usuario:**
- **Pantalla de carga** informativa durante verificación
- **Instrucciones claras** para el proceso de activación
- **Mensajes de error** descriptivos y útiles

### ✅ **Flexibilidad de desarrollo:**
- **Modo desarrollo** con UUID de prueba
- **Configuración** ajustable por entorno
- **Scripts** para resetear y probar activación

---

## 📊 **Estados de la Aplicación**

### **🔄 VERIFICANDO (Loading)**
```
- Duración: 1-3 segundos
- Acción: Verificando localStorage y configuración
- Usuario ve: Pantalla de carga con información
```

### **✅ ACTIVADO (Authorized)**
```
- Condición: Clave válida encontrada en localStorage
- Acción: Abrir aplicación principal directamente
- Usuario ve: Battle Strategy Creator completo
```

### **❌ NO ACTIVADO (Unauthorized)**
```
- Condición: Sin activación o activación inválida
- Acción: Mostrar pantalla de activación
- Usuario ve: Formulario de activación con UUID
```

---

## 🚨 **Solución de Problemas**

### **Problema: "La aplicación no se abre"**
**Causa:** Verificación de licencia fallando
**Solución:**
1. Verificar que Node.js esté instalado
2. Comprobar que las dependencias estén instaladas
3. Revisar consola del navegador para errores

### **Problema: "Pantalla de activación aparece siempre"**
**Causa:** Activación no se está guardando
**Solución:**
1. Verificar que localStorage funcione
2. Comprobar permisos del navegador
3. Probar en modo incógnito

### **Problema: "UUID cambia constantemente"**
**Causa:** Modo desarrollo generando UUID aleatorio
**Solución:**
1. Esto es normal en desarrollo
2. En producción usar UUID real del sistema
3. Para pruebas, usar UUID fijo

### **Problema: "Clave válida no funciona"**
**Causa:** UUID o clave secreta incorrectos
**Solución:**
1. Verificar UUID exacto (copiar/pegar)
2. Confirmar clave secreta: `Lucas2Derepredador2025`
3. Regenerar clave si es necesario

---

## 📈 **Métricas y Monitoreo**

### **Eventos importantes a monitorear:**
- ✅ Activaciones exitosas
- ❌ Intentos de activación fallidos
- 🔄 Verificaciones de licencia al inicio
- ⚠️ Detecciones de manipulación

### **Logs recomendados:**
```javascript
// En producción, implementar logging
console.log('Activation attempt:', {
  uuid: uuid.substring(0, 8) + '...',
  success: isValid,
  timestamp: new Date().toISOString()
});
```

---

## 🎯 **Resumen**

**Battle Strategy Creator ahora:**
- ✅ **Verifica la licencia ANTES de abrir**
- ✅ **Protege contra acceso no autorizado**
- ✅ **Proporciona experiencia clara al usuario**
- ✅ **Incluye herramientas de administración**
- ✅ **Permite pruebas y desarrollo fácil**

**El sistema garantiza que solo usuarios con claves válidas puedan acceder a la aplicación completa.** 🛡️
