// Script para generar claves de activación
// Uso: node scripts/generateActivationKey.js <UUID>

import CryptoJS from 'crypto-js';

// Clave secreta ofuscada - debe coincidir con la del cliente
const getSecretKey = () => {
  const parts = ['Lucas2D', 'r', 'pr', 'dador2025'];
  const vowel = String.fromCharCode(101); // 'e'
  return parts[0] + vowel + parts[1] + vowel + parts[2] + vowel + parts[3];
};

// Función para generar HMAC SHA256
const generateHMAC = (message) => {
  const secretKey = getSecretKey();
  const hash = CryptoJS.HmacSHA256(message, secretKey);
  return hash.toString(CryptoJS.enc.Hex);
};

// Función para generar clave de activación basada en UUID
const generateActivationKey = (uuid) => {
  if (!uuid || uuid.trim() === '') {
    throw new Error('UUID no puede estar vacío');
  }
  
  // Normalizar UUID (remover guiones y convertir a mayúsculas)
  const normalizedUUID = uuid.replace(/-/g, '').toUpperCase();
  
  // Generar HMAC del UUID
  const hmac = generateHMAC(normalizedUUID);
  
  // Tomar los primeros 16 caracteres y formatear como clave
  const keyPart = hmac.substring(0, 16).toUpperCase();
  
  // Formatear como XXXX-XXXX-XXXX-XXXX
  return `${keyPart.substring(0, 4)}-${keyPart.substring(4, 8)}-${keyPart.substring(8, 12)}-${keyPart.substring(12, 16)}`;
};

// Función principal
const main = () => {
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    console.log('Uso: node generateActivationKey.js <UUID>');
    console.log('Ejemplo: node generateActivationKey.js 12345678-1234-1234-1234-123456789012');
    process.exit(1);
  }
  
  const uuid = args[0];
  
  try {
    const activationKey = generateActivationKey(uuid);
    
    console.log('='.repeat(50));
    console.log('GENERADOR DE CLAVES DE ACTIVACIÓN');
    console.log('='.repeat(50));
    console.log(`UUID de entrada: ${uuid}`);
    console.log(`Clave de activación: ${activationKey}`);
    console.log('='.repeat(50));
    console.log('IMPORTANTE: Guarda esta clave de forma segura');
    console.log('='.repeat(50));
    
  } catch (error) {
    console.error('Error:', error.message);
    process.exit(1);
  }
};

// Ejecutar si es llamado directamente
main();

export {
  generateActivationKey,
  generateHMAC
};
