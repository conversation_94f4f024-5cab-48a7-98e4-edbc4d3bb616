# Sistema de Protección Battle Strategy Creator

Este proyecto incluye un sistema de protección robusto basado en HMAC SHA256 que requiere activación por UUID del sistema.

## 🔐 Características de Seguridad

- **Cifrado HMAC SHA256**: Utiliza una clave secreta ofuscada para generar códigos de autenticación
- **UUID del Sistema**: Cada activación está vinculada al UUID único de la PC
- **Clave Secreta Ofuscada**: La clave `Lucas2Derepredador2025` está ofuscada en el código
- **PowerShell Oculto**: Obtiene el UUID usando PowerShell en modo oculto
- **Validación Local**: La activación se almacena localmente después de la validación

## 🚀 Cómo Funciona

1. **Al iniciar la aplicación**: Se obtiene el UUID del sistema usando PowerShell
2. **Pantalla de Activación**: Se muestra el UUID y un campo para la clave de activación
3. **Validación**: La clave se valida usando HMAC SHA256 contra el UUID
4. **Activación**: Si es válida, se guarda el estado de activación localmente

## 🛠️ Generación de Claves de Activación

### Para el Desarrollador:

```bash
# Instalar dependencias para el script
npm install crypto-js

# Generar clave para un UUID específico
node scripts/generateActivationKey.js "12345678-1234-1234-1234-123456789012"
```

### Ejemplo de Salida:
```
==================================================
GENERADOR DE CLAVES DE ACTIVACIÓN
==================================================
UUID de entrada: 12345678-1234-1234-1234-123456789012
Clave de activación: A1B2-C3D4-E5F6-7890
==================================================
IMPORTANTE: Guarda esta clave de forma segura
==================================================
```

## 📁 Estructura de Archivos

```
├── utils/
│   └── crypto.ts              # Utilidades de cifrado HMAC
├── components/
│   └── ActivationScreen.tsx   # Pantalla de activación
├── hooks/
│   └── useActivation.ts       # Hook para manejo de activación
├── scripts/
│   ├── generateActivationKey.js  # Generador de claves (servidor)
│   └── getSystemUUID.ps1         # Script PowerShell para UUID
└── electron/                  # Configuración Electron (opcional)
    ├── main.js
    └── preload.js
```

## 🔧 Configuración

### Para Aplicación Web:
La aplicación web usa un UUID de prueba generado aleatoriamente. Para producción, considera usar Electron.

### Para Aplicación Electron:
1. Instalar Electron: `npm install --save-dev electron`
2. Agregar scripts al package.json:
```json
{
  "scripts": {
    "electron": "electron electron/main.js",
    "electron-dev": "NODE_ENV=development electron electron/main.js"
  }
}
```

## 🛡️ Medidas de Seguridad Implementadas

### 1. Ofuscación de Clave Secreta
```typescript
const getSecretKey = (): string => {
  const parts = ['Lucas2D', 'r', 'pr', 'dador2025'];
  const vowel = String.fromCharCode(101); // 'e'
  return parts[0] + vowel + parts[1] + vowel + parts[2] + vowel + parts[3];
};
```

### 2. PowerShell Oculto
```powershell
Get-WmiObject -Class Win32_ComputerSystemProduct | Select-Object -Property UUID
```

### 3. Validación HMAC
```typescript
const validateActivationKey = (uuid: string, activationKey: string): boolean => {
  const expectedKey = generateActivationKey(uuid);
  return expectedKey.toLowerCase() === activationKey.toLowerCase();
};
```

## 📋 Proceso de Activación para Usuarios

1. **Ejecutar la aplicación**
2. **Copiar el UUID mostrado** (botón 📋)
3. **Enviar el UUID al desarrollador** para obtener la clave
4. **Introducir la clave de activación** en formato XXXX-XXXX-XXXX-XXXX
5. **Hacer clic en "Activar"**

## 🔍 Troubleshooting

### Error: "No se puede obtener UUID en entorno web"
- **Solución**: Usar la versión Electron o implementar un método alternativo

### Error: "Clave de activación inválida"
- **Verificar**: Que el UUID sea correcto
- **Verificar**: Que la clave esté en el formato correcto
- **Verificar**: Que no haya espacios adicionales

### Error: "Error al obtener el UUID del sistema"
- **Verificar**: Que PowerShell esté disponible
- **Verificar**: Permisos de ejecución
- **Alternativa**: Usar el UUID de prueba para desarrollo

## 🚨 Notas de Seguridad

- **NO** incluyas claves de activación en el código fuente
- **NO** compartas el script generador con usuarios finales
- **SÍ** mantén el script generador en un servidor seguro
- **SÍ** considera implementar un sistema de activación online para mayor seguridad

## 📞 Soporte

Para generar nuevas claves de activación o resolver problemas de activación, contacta al desarrollador con el UUID del sistema.
