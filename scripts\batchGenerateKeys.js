// Script para generar múltiples claves de activación desde un archivo
// Uso: node scripts/batchGenerateKeys.js <archivo_uuids.txt>

import { generateActivationKey } from './generateActivationKey.js';
import fs from 'fs';
import path from 'path';

const main = () => {
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    console.log('Uso: node batchGenerateKeys.js <archivo_uuids.txt>');
    console.log('');
    console.log('El archivo debe contener un UUID por línea:');
    console.log('12345678-1234-1234-1234-123456789012');
    console.log('*************-4321-4321-************');
    console.log('...');
    process.exit(1);
  }
  
  const filePath = args[0];
  
  try {
    // Verificar que el archivo existe
    if (!fs.existsSync(filePath)) {
      throw new Error(`Archivo no encontrado: ${filePath}`);
    }
    
    // Leer el archivo
    const content = fs.readFileSync(filePath, 'utf8');
    const uuids = content
      .split('\n')
      .map(line => line.trim())
      .filter(line => line.length > 0);
    
    if (uuids.length === 0) {
      throw new Error('El archivo no contiene UUIDs válidos');
    }
    
    console.log('='.repeat(70));
    console.log('GENERADOR MASIVO DE CLAVES DE ACTIVACIÓN');
    console.log('='.repeat(70));
    console.log(`Archivo: ${filePath}`);
    console.log(`UUIDs encontrados: ${uuids.length}`);
    console.log('='.repeat(70));
    
    const results = [];
    let successCount = 0;
    let errorCount = 0;
    
    uuids.forEach((uuid, index) => {
      try {
        const activationKey = generateActivationKey(uuid);
        const result = {
          index: index + 1,
          uuid,
          activationKey,
          status: 'success'
        };
        results.push(result);
        successCount++;
        
        console.log(`${index + 1:3}. UUID: ${uuid}`);
        console.log(`     Clave: ${activationKey}`);
        console.log('');
      } catch (error) {
        const result = {
          index: index + 1,
          uuid,
          error: error.message,
          status: 'error'
        };
        results.push(result);
        errorCount++;
        
        console.log(`${index + 1:3}. UUID: ${uuid}`);
        console.log(`     ERROR: ${error.message}`);
        console.log('');
      }
    });
    
    // Generar archivo de salida
    const outputPath = path.join(path.dirname(filePath), 'activation_keys.txt');
    let outputContent = 'CLAVES DE ACTIVACIÓN GENERADAS\n';
    outputContent += '='.repeat(50) + '\n';
    outputContent += `Fecha: ${new Date().toISOString()}\n`;
    outputContent += `Total UUIDs: ${uuids.length}\n`;
    outputContent += `Exitosos: ${successCount}\n`;
    outputContent += `Errores: ${errorCount}\n`;
    outputContent += '='.repeat(50) + '\n\n';
    
    results.forEach(result => {
      if (result.status === 'success') {
        outputContent += `${result.index}. UUID: ${result.uuid}\n`;
        outputContent += `   Clave: ${result.activationKey}\n\n`;
      } else {
        outputContent += `${result.index}. UUID: ${result.uuid}\n`;
        outputContent += `   ERROR: ${result.error}\n\n`;
      }
    });
    
    fs.writeFileSync(outputPath, outputContent, 'utf8');
    
    console.log('='.repeat(70));
    console.log('RESUMEN');
    console.log('='.repeat(70));
    console.log(`Total procesados: ${uuids.length}`);
    console.log(`Exitosos: ${successCount}`);
    console.log(`Errores: ${errorCount}`);
    console.log(`Archivo de salida: ${outputPath}`);
    console.log('='.repeat(70));
    
  } catch (error) {
    console.error('Error:', error.message);
    process.exit(1);
  }
};

// Función para crear un archivo de ejemplo
const createExampleFile = (filename = 'example_uuids.txt') => {
  const exampleUUIDs = [
    '12345678-1234-1234-1234-123456789012',
    '*************-4321-4321-************',
    'ABCDEFGH-ABCD-ABCD-ABCD-ABCDEFGHIJKL',
    '11111111-**************-************',
    'AAAAAAAA-BBBB-CCCC-DDDD-EEEEEEEEEEEE'
  ];
  
  const content = exampleUUIDs.join('\n') + '\n';
  fs.writeFileSync(filename, content, 'utf8');
  
  console.log(`Archivo de ejemplo creado: ${filename}`);
  console.log('Contenido:');
  exampleUUIDs.forEach((uuid, index) => {
    console.log(`${index + 1}. ${uuid}`);
  });
};

// Ejecutar función principal
main();

export { createExampleFile };
