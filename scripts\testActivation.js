// Script de prueba para el sistema de activación
import CryptoJS from 'crypto-js';

// Duplicar funciones para evitar problemas de importación
const getSecretKey = () => {
  const parts = ['Lucas2D', 'r', 'pr', 'dador2025'];
  const vowel = String.fromCharCode(101); // 'e'
  return parts[0] + vowel + parts[1] + vowel + parts[2] + vowel + parts[3];
};

const generateHMAC = (message) => {
  const secretKey = getSecretKey();
  const hash = CryptoJS.HmacSHA256(message, secretKey);
  return hash.toString(CryptoJS.enc.Hex);
};

const generateActivationKey = (uuid) => {
  if (!uuid || uuid.trim() === '') {
    throw new Error('UUID no puede estar vacío');
  }

  const normalizedUUID = uuid.replace(/-/g, '').toUpperCase();
  const hmac = generateHMAC(normalizedUUID);
  const keyPart = hmac.substring(0, 16).toUpperCase();

  return `${keyPart.substring(0, 4)}-${keyPart.substring(4, 8)}-${keyPart.substring(8, 12)}-${keyPart.substring(12, 16)}`;
};

// Función para probar el sistema de activación
const testActivationSystem = () => {
  console.log('🧪 PRUEBAS DEL SISTEMA DE ACTIVACIÓN');
  console.log('='.repeat(50));

  // Test 1: Generar clave para UUID de prueba
  const testUUID = '12345678-1234-1234-1234-123456789012';
  console.log('\n📋 Test 1: Generación de clave');
  console.log(`UUID: ${testUUID}`);
  
  try {
    const activationKey = generateActivationKey(testUUID);
    console.log(`✅ Clave generada: ${activationKey}`);
  } catch (error) {
    console.log(`❌ Error: ${error.message}`);
  }

  // Test 2: Verificar consistencia
  console.log('\n🔄 Test 2: Consistencia de generación');
  try {
    const key1 = generateActivationKey(testUUID);
    const key2 = generateActivationKey(testUUID);
    
    if (key1 === key2) {
      console.log('✅ Las claves son consistentes');
      console.log(`Clave: ${key1}`);
    } else {
      console.log('❌ Las claves no son consistentes');
      console.log(`Clave 1: ${key1}`);
      console.log(`Clave 2: ${key2}`);
    }
  } catch (error) {
    console.log(`❌ Error: ${error.message}`);
  }

  // Test 3: Diferentes UUIDs generan diferentes claves
  console.log('\n🔀 Test 3: Diferentes UUIDs');
  const testUUID2 = '*************-4321-4321-************';
  try {
    const key1 = generateActivationKey(testUUID);
    const key2 = generateActivationKey(testUUID2);
    
    if (key1 !== key2) {
      console.log('✅ UUIDs diferentes generan claves diferentes');
      console.log(`UUID 1: ${testUUID} -> ${key1}`);
      console.log(`UUID 2: ${testUUID2} -> ${key2}`);
    } else {
      console.log('❌ UUIDs diferentes generan la misma clave (problema!)');
    }
  } catch (error) {
    console.log(`❌ Error: ${error.message}`);
  }

  // Test 4: Formato de clave
  console.log('\n📏 Test 4: Formato de clave');
  try {
    const key = generateActivationKey(testUUID);
    const regex = /^[A-F0-9]{4}-[A-F0-9]{4}-[A-F0-9]{4}-[A-F0-9]{4}$/;
    
    if (regex.test(key)) {
      console.log('✅ Formato de clave correcto');
      console.log(`Formato: ${key}`);
    } else {
      console.log('❌ Formato de clave incorrecto');
      console.log(`Clave: ${key}`);
    }
  } catch (error) {
    console.log(`❌ Error: ${error.message}`);
  }

  // Test 5: Manejo de errores
  console.log('\n⚠️  Test 5: Manejo de errores');
  try {
    generateActivationKey('');
    console.log('❌ Debería haber lanzado error para UUID vacío');
  } catch (error) {
    console.log('✅ Error manejado correctamente para UUID vacío');
  }

  try {
    generateActivationKey(null);
    console.log('❌ Debería haber lanzado error para UUID null');
  } catch (error) {
    console.log('✅ Error manejado correctamente para UUID null');
  }

  // Test 6: HMAC básico
  console.log('\n🔐 Test 6: Función HMAC');
  try {
    const message = 'test message';
    const hmac1 = generateHMAC(message);
    const hmac2 = generateHMAC(message);
    
    if (hmac1 === hmac2 && hmac1.length === 64) {
      console.log('✅ HMAC funciona correctamente');
      console.log(`HMAC: ${hmac1.substring(0, 16)}...`);
    } else {
      console.log('❌ HMAC no funciona correctamente');
    }
  } catch (error) {
    console.log(`❌ Error en HMAC: ${error.message}`);
  }

  console.log('\n' + '='.repeat(50));
  console.log('🏁 PRUEBAS COMPLETADAS');
  console.log('='.repeat(50));
};

// Función para generar múltiples claves de ejemplo
const generateExampleKeys = () => {
  console.log('\n📝 CLAVES DE EJEMPLO PARA PRUEBAS');
  console.log('='.repeat(50));

  const exampleUUIDs = [
    '12345678-1234-1234-1234-123456789012',
    '*************-4321-4321-************',
    'ABCDEFGH-ABCD-ABCD-ABCD-ABCDEFGHIJKL',
    '11111111-**************-************'
  ];

  exampleUUIDs.forEach((uuid, index) => {
    try {
      const key = generateActivationKey(uuid);
      console.log(`${index + 1}. UUID: ${uuid}`);
      console.log(`   Clave: ${key}\n`);
    } catch (error) {
      console.log(`${index + 1}. UUID: ${uuid}`);
      console.log(`   Error: ${error.message}\n`);
    }
  });
};

// Ejecutar pruebas
testActivationSystem();
generateExampleKeys();

export {
  testActivationSystem,
  generateExampleKeys
};
