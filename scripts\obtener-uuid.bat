@echo off
title Obtener UUID del Sistema
color 0B

echo.
echo ========================================
echo   OBTENER UUID DEL SISTEMA
echo   Battle Strategy Creator
echo ========================================
echo.

REM Verificar si se está ejecutando como administrador
net session >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ Ejecutandose como administrador
) else (
    echo ⚠️  ADVERTENCIA: No se está ejecutando como administrador
    echo    Algunos métodos pueden fallar
    echo.
)

echo 🔍 Obteniendo UUID del sistema...
echo.

REM Método 1: Get-CimInstance (recomendado)
echo 📋 Método 1: Get-CimInstance
powershell -Command "try { $uuid = (Get-CimInstance -ClassName Win32_ComputerSystemProduct).UUID; if ($uuid -and $uuid -ne '00000000-0000-0000-0000-000000000000') { Write-Host '✅ UUID encontrado:' $uuid } else { Write-Host '❌ UUID no válido o no encontrado' } } catch { Write-Host '❌ Error:' $_.Exception.Message }"
echo.

REM Método 2: Get-WmiObject (alternativo)
echo 📋 Método 2: Get-WmiObject
powershell -Command "try { $uuid = (Get-WmiObject -Class Win32_ComputerSystemProduct).UUID; if ($uuid -and $uuid -ne '00000000-0000-0000-0000-000000000000') { Write-Host '✅ UUID encontrado:' $uuid } else { Write-Host '❌ UUID no válido o no encontrado' } } catch { Write-Host '❌ Error:' $_.Exception.Message }"
echo.

REM Método 3: Información adicional del sistema
echo 📋 Información adicional del sistema:
powershell -Command "try { $board = Get-CimInstance -ClassName Win32_BaseBoard; Write-Host 'Placa base:' $board.Manufacturer $board.Product; Write-Host 'Serial:' $board.SerialNumber } catch { Write-Host '❌ Error obteniendo info de placa base' }"
echo.

REM Método 4: Obtener UUID y guardarlo en archivo
echo 📋 Guardando UUID en archivo...
powershell -Command "try { $uuid = (Get-CimInstance -ClassName Win32_ComputerSystemProduct).UUID; if ($uuid -and $uuid -ne '00000000-0000-0000-0000-000000000000') { $uuid | Out-File -FilePath 'uuid_sistema.txt' -Encoding UTF8; Write-Host '✅ UUID guardado en uuid_sistema.txt' } else { Write-Host '❌ No se pudo guardar UUID válido' } } catch { Write-Host '❌ Error guardando UUID' }"

if exist "uuid_sistema.txt" (
    echo.
    echo 📄 Contenido del archivo uuid_sistema.txt:
    type uuid_sistema.txt
    echo.
    echo 💡 Usa este UUID para generar tu clave de activación:
    echo.
    echo    cd generadorkey
    for /f "delims=" %%i in (uuid_sistema.txt) do echo    npm run generar "%%i" "Lucas2Derepredador2025"
    echo.
)

echo ========================================
echo.
echo 💡 INSTRUCCIONES:
echo.
echo 1. Copia el UUID mostrado arriba
echo 2. Ve a la carpeta generadorkey
echo 3. Ejecuta: npm run generar "TU-UUID" "Lucas2Derepredador2025"
echo 4. Usa la clave generada en la aplicación
echo.
echo ⚠️  IMPORTANTE:
echo - Ejecuta este script como administrador para mejores resultados
echo - El UUID debe ser siempre el mismo para tu PC
echo - Si el UUID es 00000000-0000-0000-0000-000000000000, 
echo   tu sistema no tiene UUID de hardware válido
echo.
echo ========================================
echo.
pause
