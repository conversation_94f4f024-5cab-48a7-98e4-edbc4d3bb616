// Utilidades para leer UUID del sistema desde diferentes fuentes

// Función para leer UUID desde archivo generado por script .bat
export const readUUIDFromFile = async (): Promise<string | null> => {
  try {
    // Intentar leer el archivo uuid_sistema.txt
    const response = await fetch('/uuid_sistema.txt');
    if (response.ok) {
      const uuid = await response.text();
      const cleanUUID = uuid.trim().toUpperCase();
      
      // Validar formato UUID
      if (cleanUUID.match(/^[0-9A-F]{8}-[0-9A-F]{4}-[0-9A-F]{4}-[0-9A-F]{4}-[0-9A-F]{12}$/)) {
        return cleanUUID;
      }
    }
  } catch (error) {
    console.log('No se pudo leer archivo UUID:', error.message);
  }
  
  return null;
};

// Función para mostrar instrucciones de cómo obtener UUID real
export const showUUIDInstructions = (): string => {
  return `
🔍 CÓMO OBTENER EL UUID REAL DE TU SISTEMA:

Método 1: Script automático
1. Ejecuta "scripts/obtener-uuid.bat" como administrador
2. Copia el UUID mostrado
3. Úsalo para generar tu clave de activación

Método 2: PowerShell manual
1. Abre PowerShell como administrador
2. Ejecuta: Get-CimInstance -ClassName Win32_ComputerSystemProduct | Select-Object -ExpandProperty UUID
3. Copia el UUID mostrado

Método 3: Comando alternativo
1. Abre PowerShell como administrador  
2. Ejecuta: (Get-WmiObject -Class Win32_ComputerSystemProduct).UUID
3. Copia el UUID mostrado

⚠️ IMPORTANTE:
- Debe ejecutarse como administrador
- El UUID debe ser siempre el mismo para tu PC
- Si obtienes 00000000-0000-0000-0000-000000000000, tu sistema no tiene UUID válido

💡 Una vez que tengas el UUID real, úsalo en el generador de claves:
cd generadorkey
npm run generar "TU-UUID-REAL" "Lucas2Derepredador2025"
`;
};

// Función para validar si un UUID es real (no es el UUID nulo)
export const isValidRealUUID = (uuid: string): boolean => {
  if (!uuid || uuid.length !== 36) {
    return false;
  }
  
  // Verificar que no sea el UUID nulo
  if (uuid === '00000000-0000-0000-0000-000000000000') {
    return false;
  }
  
  // Verificar formato UUID válido
  return /^[0-9A-F]{8}-[0-9A-F]{4}-[0-9A-F]{4}-[0-9A-F]{4}-[0-9A-F]{12}$/i.test(uuid);
};

// Función para detectar si estamos en un entorno que puede obtener UUID real
export const canGetRealUUID = (): boolean => {
  // Verificar si estamos en Electron
  if ((window as any).electronAPI) {
    return true;
  }
  
  // Verificar si estamos en un entorno que soporta ejecución de comandos
  if (typeof process !== 'undefined' && process.platform) {
    return true;
  }
  
  return false;
};

// Función para mostrar estado del UUID actual
export const getUUIDStatus = (uuid: string): {
  isReal: boolean;
  isValid: boolean;
  message: string;
  instructions?: string;
} => {
  if (!uuid) {
    return {
      isReal: false,
      isValid: false,
      message: 'No hay UUID disponible',
      instructions: showUUIDInstructions()
    };
  }
  
  if (!isValidRealUUID(uuid)) {
    return {
      isReal: false,
      isValid: false,
      message: 'UUID no válido o es UUID nulo',
      instructions: showUUIDInstructions()
    };
  }
  
  // Verificar si es un UUID generado por el sistema o uno consistente
  const isFromCache = localStorage.getItem('system_uuid_is_real') === 'true';
  
  if (isFromCache) {
    return {
      isReal: true,
      isValid: true,
      message: 'UUID real del hardware obtenido correctamente'
    };
  } else {
    return {
      isReal: false,
      isValid: true,
      message: 'UUID generado de forma consistente (no es UUID real del hardware)',
      instructions: showUUIDInstructions()
    };
  }
};
